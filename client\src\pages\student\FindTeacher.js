import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout';
import ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';
import {
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Box,
  Rating,
  Slider,
  styled,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  ListItemText,
  Checkbox,
  IconButton,
  useTheme,
  useMediaQuery,
  CircularProgress,
  Pagination,
  Drawer,
  Chip,
  Avatar,
  Paper,
  Collapse,
  Divider,
  alpha,
  Tooltip,
  FormControlLabel,
  Alert,
  useMediaQuery as useMuiMediaQuery,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Language as LanguageIcon,
  School as SchoolIcon,
  AttachMoney as MoneyIcon,
  Star as StarIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  AccessTime as AccessTimeIcon,
  Verified as VerifiedIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';

// Custom styled components for enhanced price range filter
const PriceRangeContainer = styled(Box)(({ theme, isRtl, isMobile }) => ({
  padding: theme.spacing(isMobile ? 2 : 3),
  borderRadius: theme.shape.borderRadius * 2,
  background: `linear-gradient(145deg, ${alpha(theme.palette.background.paper, 0.9)}, ${alpha(theme.palette.background.paper, 0.7)})`,
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  boxShadow: `0 10px 30px -10px ${alpha(theme.palette.common.black, 0.1)}`,
  transition: 'all 0.3s ease-in-out',
  direction: isRtl ? 'rtl' : 'ltr',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: `0 15px 35px -10px ${alpha(theme.palette.common.black, 0.15)}`,
  },
  [theme.breakpoints.down('sm')]: {
    margin: theme.spacing(0, 1),
  }
}));

const CustomSlider = styled(Slider)(({ theme, isRtl, isMobile }) => ({
  color: theme.palette.primary.main,
  height: 8,
  padding: '15px 0',
  direction: isRtl ? 'rtl' : 'ltr',
  '& .MuiSlider-track': {
    height: 8,
    background: `linear-gradient(${isRtl ? '-90deg' : '90deg'}, ${theme.palette.primary.main}, ${theme.palette.primary.light})`,
    border: 'none',
    boxShadow: `0 0 10px ${alpha(theme.palette.primary.main, 0.5)}`,
  },
  '& .MuiSlider-thumb': {
    height: isMobile ? 28 : 24,
    width: isMobile ? 28 : 24,
    backgroundColor: theme.palette.background.paper,
    border: `2px solid ${theme.palette.primary.main}`,
    boxShadow: `0 0 15px ${alpha(theme.palette.primary.main, 0.3)}`,
    '&:focus, &:hover': {
      boxShadow: `0 0 20px ${alpha(theme.palette.primary.main, 0.5)}`,
    },
    '&::before': {
      boxShadow: '0 2px 12px 0 rgba(0,0,0,0.4)',
    },
    [theme.breakpoints.down('sm')]: {
      '&:focus, &:hover, &.Mui-active': {
        boxShadow: `0 0 15px ${alpha(theme.palette.primary.main, 0.4)}`,
      },
    },
  },
  '& .MuiSlider-valueLabel': {
    fontSize: isMobile ? 16 : 14,
    fontWeight: 'bold',
    top: -6,
    backgroundColor: theme.palette.primary.main,
    padding: theme.spacing(0.5, 1),
    '&::before': {
      display: 'none',
    },
    '& *': {
      background: 'transparent',
      color: theme.palette.common.white,
    },
    [theme.breakpoints.down('sm')]: {
      fontSize: 14,
      padding: theme.spacing(0.5),
    },
  },
  '& .MuiSlider-rail': {
    height: 8,
    background: `linear-gradient(${isRtl ? '-90deg' : '90deg'}, ${alpha(theme.palette.grey[300], 0.3)}, ${alpha(theme.palette.grey[400], 0.3)})`,
    borderRadius: 4,
  },
  '& .MuiSlider-mark': {
    backgroundColor: theme.palette.primary.main,
    height: 12,
    width: 2,
    '&.MuiSlider-markActive': {
      opacity: 1,
      backgroundColor: theme.palette.common.white,
    },
  },
  [theme.breakpoints.down('sm')]: {
    height: 10,
    '& .MuiSlider-track': {
      height: 10,
    },
    '& .MuiSlider-rail': {
      height: 10,
    },
  },
}));

const PriceDisplay = styled(Typography)(({ theme, isRtl, isMobile }) => ({
  fontSize: isMobile ? '1.1rem' : '1.25rem',
  fontWeight: 600,
  color: theme.palette.primary.main,
  textAlign: 'center',
  marginTop: theme.spacing(2),
  direction: isRtl ? 'rtl' : 'ltr',
  transition: 'all 0.3s ease',
  '& .currency': {
    fontSize: '0.9em',
    opacity: 0.8,
    marginRight: isRtl ? theme.spacing(0.5) : 0,
    marginLeft: isRtl ? 0 : theme.spacing(0.5),
  },
  [theme.breakpoints.down('sm')]: {
    marginTop: theme.spacing(1.5),
  }
}));

const FindTeacher = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser, token } = useAuth();

  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isRtl = i18n.language === 'ar';
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    subjects: [], // Will store subject IDs
    languages: [],
    priceRange: [0, 1000],
    rating: '',
  });
  const [appliedFilters, setAppliedFilters] = useState({
    subjects: [], // Will store subject IDs
    languages: [],
    priceRange: [0, 1000],
    rating: '',
  });
  const [showFilters, setShowFilters] = useState(!isMobile);
  const [loading, setLoading] = useState(true);
  const [teachers, setTeachers] = useState([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchFilters, setSearchFilters] = useState({
    categories: [],
    languages: [],
    priceRange: { min: 0, max: 1000 }
  });

  const fullScreen = useMuiMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    const fetchFilters = async () => {
      try {
        const { data } = await axios.get('/api/search/filters');
        if (data.success) {
          const processedFilters = {
            categories: data.data.categories,
            languages: data.data.languages,
            priceRange: {
              min: data.data.priceRange.min || 0,
              max: data.data.priceRange.max || 1000
            }
          };
          setSearchFilters(processedFilters);
          setFilters(prev => ({
            ...prev,
            priceRange: [processedFilters.priceRange.min, processedFilters.priceRange.max]
          }));
          setAppliedFilters(prev => ({
            ...prev,
            priceRange: [processedFilters.priceRange.min, processedFilters.priceRange.max]
          }));
        }
      } catch (error) {
        console.error('Error fetching filters:', error);
        setError(t('errors.filtersFetchFailed'));
      }
    };

    fetchFilters();
  }, [t]);

  // Function to fetch teachers
  const fetchTeachers = async () => {
    try {
      setLoading(true);
      setError('');

      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', page);
      params.append('limit', 10);

      // Add filter parameters if they are not empty
      if (appliedFilters.subjects.length > 0) {
        params.append('subjects', JSON.stringify(appliedFilters.subjects));
      }

      if (appliedFilters.languages.length > 0) {
        params.append('languages', JSON.stringify(appliedFilters.languages));
      }

      if (appliedFilters.priceRange.length === 2) {
        params.append('priceRange', JSON.stringify(appliedFilters.priceRange));
      }

      if (appliedFilters.rating) {
        params.append('rating', appliedFilters.rating);
      }

      console.log('Sending filters to API:', params.toString()); // Debug log

      if (!token) {
        throw new Error('No authentication token found');
      }

      const { data } = await axios.get(`/api/search/teachers?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        console.log('Teachers data:', data.data.teachers);
        console.log('Search filters:', searchFilters);
        setTeachers(data.data.teachers);
        setTotalPages(data.data.pagination.pages);
      }
    } catch (error) {
      console.error('Error fetching teachers:', error);
      setError(error.response?.data?.message || t('errors.teachersFetchFailed'));
      setTeachers([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // Effect to fetch teachers when page or filters change
  useEffect(() => {
    if (currentUser && token && appliedFilters) {
      fetchTeachers();
    }
  }, [page, currentUser, token, appliedFilters]);

  const handleSearch = () => {
    console.log('Current filters:', filters); // Debug log
    setAppliedFilters(filters);
    setPage(1);
    // No need to call fetchTeachers() here as it will be triggered by the useEffect
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const formatPriceLabel = (value) => {
    return isRtl ? `${value}$` : `$${value}`;
  };

  const handlePriceRangeChange = (event, newValue) => {
    setFilters(prev => ({
      ...prev,
      priceRange: newValue
    }));
  };

  const handleClearFilters = () => {
    const defaultFilters = {
      subjects: [],
      languages: [],
      priceRange: [searchFilters.priceRange.min, searchFilters.priceRange.max],
      rating: ''
    };
    setFilters(defaultFilters);
    setAppliedFilters(defaultFilters);
    setPage(1);
  };

  const getPriceRangeText = (value) => {
    return `$${value[0]} - $${value[1]}`;
  };

  // Add this new function to render the price range filter
  const renderPriceRangeFilter = () => (
    <PriceRangeContainer isRtl={isRtl} isMobile={isMobile}>
      <Typography
        variant="h6"
        gutterBottom
        sx={{
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          mb: 2,
          direction: isRtl ? 'rtl' : 'ltr',
          justifyContent: isRtl ? 'flex-end' : 'flex-start',
        }}
      >
        <MoneyIcon color="primary" sx={{ transform: isRtl ? 'scaleX(-1)' : 'none' }} />
        {t('search.priceRange')}
      </Typography>
      <Box sx={{
        px: isMobile ? 1 : 2,
        pb: 1,
        direction: isRtl ? 'rtl' : 'ltr'
      }}>
        <CustomSlider
          value={filters.priceRange}
          onChange={handlePriceRangeChange}
          valueLabelDisplay="on"
          valueLabelFormat={formatPriceLabel}
          min={searchFilters.priceRange.min}
          max={searchFilters.priceRange.max}
          isRtl={isRtl}
          isMobile={isMobile}
          marks={[
            {
              value: searchFilters.priceRange.min,
              label: formatPriceLabel(searchFilters.priceRange.min)
            },
            {
              value: searchFilters.priceRange.max,
              label: formatPriceLabel(searchFilters.priceRange.max)
            }
          ]}
        />
      </Box>
      <PriceDisplay isRtl={isRtl}>
        {isRtl ? (
          <>
            {filters.priceRange[1]}<span className="currency">$</span> - {filters.priceRange[0]}<span className="currency">$</span>
          </>
        ) : (
          <>
            <span className="currency">$</span>{filters.priceRange[0]} - <span className="currency">$</span>{filters.priceRange[1]}
          </>
        )}
      </PriceDisplay>
    </PriceRangeContainer>
  );

  // Function to check if a URL is a local video file
  const isLocalVideoFile = (url) => {
    if (!url) return false;
    return url.startsWith('/uploads/') && (
      url.endsWith('.mp4') ||
      url.endsWith('.webm') ||
      url.endsWith('.ogg')
    );
  };

  // Function to check if a URL is a YouTube video
  const isYoutubeVideo = (url) => {
    if (!url) return false;
    return url.includes('youtube.com') || url.includes('youtu.be');
  };

  // Function to extract YouTube video ID from URL
  const getYoutubeVideoId = (url) => {
    if (!url) return null;

    try {
      // Regular expressions to match different YouTube URL formats
      const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
      const match = url.match(regExp);

      if (match && match[2] && match[2].length === 11) {
        return match[2];
      }

      // If the URL is already a video ID (11 characters)
      if (url.length === 11 && /^[a-zA-Z0-9_-]{11}$/.test(url)) {
        return url;
      }

      console.log('Could not extract YouTube video ID from:', url);
      return null;
    } catch (error) {
      console.error('Error extracting YouTube video ID:', error);
      return null;
    }
  };

  return (
    <Layout>
      <Container maxWidth="xl" sx={{ py: 8, mt: { xs: 4, md: 6 } }}>
        <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>
          {/* Page Title */}
          <Paper
            elevation={0}
            sx={{
              p: 3,
              mb: 3,
              textAlign: 'center',
              background: theme.palette.primary.main,
              color: 'white'
            }}
          >
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 'bold',
                fontFamily: isRtl ? '"Noto Kufi Arabic", sans-serif' : 'inherit'
              }}
            >
              {t('search.findTeacher')}
            </Typography>
          </Paper>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

        {/* Main Content */}
        <Grid container spacing={3}>
          {/* Filters */}
          <Grid item xs={12} md={3}>
            <Paper
              elevation={3}
              sx={{
                p: 2,
                position: { md: 'sticky' },
                top: { md: 80 },
                borderRadius: 2,
                bgcolor: theme.palette.background.default
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" component="h2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <FilterListIcon color="primary" />
                  {t('search.filters')}
                </Typography>
                {isMobile && (
                  <IconButton onClick={() => setShowFilters(!showFilters)}>
                    {showFilters ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </IconButton>
                )}
              </Box>

              <Collapse in={showFilters}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                  {/* Subject Filter */}
                  <FormControl fullWidth variant="outlined">
                    <InputLabel>{t('search.subjects')}</InputLabel>
                    <Select
                      multiple
                      value={filters.subjects}
                      onChange={(e) => handleFilterChange('subjects', e.target.value)}
                      label={t('search.subjects')}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip
                              key={value}
                              label={searchFilters.categories.find(cat => cat.id === value)?.name || value}
                              size="small"
                            />
                          ))}
                        </Box>
                      )}
                      MenuProps={{
                        PaperProps: {
                          style: {
                            maxHeight: 250
                          }
                        }
                      }}
                    >
                      {(searchFilters.categories || []).map((category) => (
                        <MenuItem
                          key={category.id}
                          value={category.id}
                        >
                          <Checkbox
                            checked={filters.subjects.includes(category.id)}
                          />
                          <ListItemText primary={category.name} />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  {/* Language Filter */}
                  <FormControl fullWidth variant="outlined">
                    <InputLabel>{t('search.languages')}</InputLabel>
                    <Select
                      multiple
                      value={filters.languages}
                      onChange={(e) => handleFilterChange('languages', e.target.value)}
                      label={t('search.languages')}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={value} size="small" />
                          ))}
                        </Box>
                      )}
                      MenuProps={{
                        PaperProps: {
                          style: {
                            maxHeight: 250
                          }
                        }
                      }}
                    >
                      {(searchFilters.languages || []).map((language) => (
                        <MenuItem key={language} value={language}>
                          <Checkbox checked={filters.languages.includes(language)} />
                          <ListItemText primary={language} />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  {/* Price Range Filter */}
                  {renderPriceRangeFilter()}

                  {/* Rating Filter */}
                  <FormControl fullWidth variant="outlined">
                    <InputLabel>{t('search.rating')}</InputLabel>
                    <Select
                      value={filters.rating}
                      label={t('search.rating')}
                      onChange={(e) => handleFilterChange('rating', e.target.value)}
                    >
                      <MenuItem value="">{t('search.anyRating')}</MenuItem>
                      {[4, 3, 2].map((rating) => (
                        <MenuItem key={rating} value={rating}>
                          <Rating value={rating} readOnly size="small" /> {t('search.andAbove')}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  {/* Search and Clear Buttons */}
                  <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      fullWidth
                      onClick={handleSearch}
                      startIcon={<SearchIcon />}
                      sx={{ borderRadius: 2, py: 1 }}
                    >
                      {t('search.searchButton')}
                    </Button>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                    <Button
                      variant="outlined"
                      color="secondary"
                      fullWidth
                      onClick={handleClearFilters}
                      sx={{ borderRadius: 2, py: 1 }}
                    >
                      {t('search.clearFilters')}
                    </Button>
                  </Box>
                </Box>
              </Collapse>
            </Paper>
          </Grid>

          {/* Teacher List */}
          <Grid item xs={12} md={9}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
                <CircularProgress />
              </Box>
            ) : teachers.length === 0 ? (
              <Paper
                sx={{
                  p: 4,
                  textAlign: 'center',
                  borderRadius: 2,
                  bgcolor: theme.palette.background.default
                }}
              >
                <Typography variant="h6" color="textSecondary">
                  {t('search.noTeachersFound')}
                </Typography>
              </Paper>
            ) : (
              <Grid container spacing={3}>
                {teachers.map((teacher) => (
                  <Grid item xs={12} key={teacher.id}>
                    <Card
                      sx={{
                        borderRadius: 2,
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: 6
                        }
                      }}
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Grid container spacing={3}>
                          {/* Teacher Image and Basic Info */}
                          <Grid item xs={12} sm={2}>
                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                              <Avatar
                                src={teacher.profile_picture_url}
                                alt={teacher.full_name}
                                sx={{
                                  width: 80,
                                  height: 80,
                                  border: `3px solid ${theme.palette.primary.main}`
                                }}
                              />
                              <Box sx={{ textAlign: 'center' }}>
                                <Typography
                                  variant="h6"
                                  sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 0.5,
                                    justifyContent: 'center',
                                    fontSize: '0.9rem'
                                  }}
                                >
                                  {teacher.full_name}
                                  {teacher.is_verified && (
                                    <Tooltip title={t('search.verifiedTeacher')}>
                                      <VerifiedIcon color="primary" fontSize="small" />
                                    </Tooltip>
                                  )}
                                </Typography>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5 }}>
                                  <Rating value={teacher.average_rating} precision={0.5} readOnly size="small" />
                                  <Typography variant="body2" color="text.secondary">
                                    ({teacher.review_count})
                                  </Typography>
                                </Box>
                              </Box>
                            </Box>
                          </Grid>

                          {/* Teacher Details */}
                          <Grid item xs={12} sm={4.5}>
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                              {/* Subjects */}
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                                <SchoolIcon fontSize="small" color="primary" />
                                {(teacher.subjects || []).map((subject) => (
                                  <Chip
                                    key={subject}
                                    label={subject}
                                    size="small"
                                    sx={{
                                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                                      color: 'primary.main'
                                    }}
                                  />
                                ))}
                              </Box>

                              {/* Languages */}
                              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <LanguageIcon fontSize="small" color="primary" />
                                  <Typography variant="body2" color="text.secondary">
                                    {t('search.teachingLanguages')}:
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, pl: 4 }}>
                                  {(teacher.teaching_languages || []).map((language) => (
                                    <Typography
                                      key={language}
                                      variant="body2"
                                      sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: 1,
                                        '&:before': {
                                          content: '""',
                                          width: '4px',
                                          height: '4px',
                                          borderRadius: '50%',
                                          backgroundColor: 'primary.main',
                                          display: 'inline-block'
                                        }
                                      }}
                                    >
                                      {language}
                                    </Typography>
                                  ))}
                                </Box>
                              </Box>

                              {/* Experience and Qualifications */}
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{
                                  display: '-webkit-box',
                                  WebkitLineClamp: 3,
                                  WebkitBoxOrient: 'vertical',
                                  overflow: 'hidden'
                                }}
                              >
                                {teacher.qualifications}
                              </Typography>

                              {/* Experience */}
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <AccessTimeIcon fontSize="small" color="primary" />
                                <Typography variant="body2">
                                  {t('search.yearsOfExperience', { years: teacher.teaching_experience })}
                                </Typography>
                              </Box>
                            </Box>
                          </Grid>

                          {/* Price, Action and Video */}
                          <Grid item xs={12} sm={5.5}>
                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                              <Typography
                                variant="h5"
                                color="primary"
                                sx={{
                                  fontWeight: 'bold',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 0.5,
                                  mb: 0.5
                                }}
                              >
                                <MoneyIcon />
                                ${teacher.price_per_lesson}/hr
                              </Typography>
                              {teacher.trial_lesson_price && (
                                <Typography variant="body2" color="text.secondary">
                                  Trial: ${teacher.trial_lesson_price}
                                </Typography>
                              )}

                              <Button
                                variant="outlined"
                                color="secondary"
                                fullWidth
                                component={Link}
                                to={`/student/teacher/${teacher.id}`}
                                startIcon={<VisibilityIcon />}
                                sx={{
                                  borderRadius: 2,
                                  py: 1
                                }}
                              >
                                {t('search.viewProfile')}
                              </Button>

                              {/* Teacher Video Preview */}
                              {teacher.intro_video_url && (
                                <Box sx={{
                                  width: '100%',
                                  mt: 2,
                                  borderRadius: 3,
                                  overflow: 'hidden',
                                  border: `3px solid ${theme.palette.primary.main}`,
                                  bgcolor: 'background.paper',
                                  boxShadow: 4,
                                  transition: 'transform 0.2s, box-shadow 0.2s',
                                  '&:hover': {
                                    transform: 'scale(1.02)',
                                    boxShadow: 6
                                  }
                                }}>
                                  <Box sx={{
                                    position: 'relative',
                                    width: '100%',
                                    height: '320px' // Larger fixed height for better visibility
                                  }}>
                                    {isYoutubeVideo(teacher.intro_video_url) ? (
                                      // YouTube video embed
                                      <iframe
                                        src={`https://www.youtube.com/embed/${getYoutubeVideoId(teacher.intro_video_url)}`}
                                        title="Teacher intro video"
                                        frameBorder="0"
                                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                        allowFullScreen
                                        style={{
                                          width: '100%',
                                          height: '100%',
                                          border: 'none',
                                          borderRadius: '8px'
                                        }}
                                      />
                                    ) : isLocalVideoFile(teacher.intro_video_url) ? (
                                      // Local video file
                                      <video
                                        style={{
                                          width: '100%',
                                          height: '100%',
                                          objectFit: 'cover',
                                          borderRadius: '8px'
                                        }}
                                        src={`https://allemnionline.com${teacher.intro_video_url}`}
                                        controls
                                        preload="metadata"
                                      />
                                    ) : (
                                      // External video URL (not YouTube)
                                      <Box sx={{
                                        position: 'absolute',
                                        top: '50%',
                                        left: '50%',
                                        transform: 'translate(-50%, -50%)',
                                        textAlign: 'center',
                                        p: 2
                                      }}>
                                        <Typography variant="body2" color="text.secondary">
                                          <a
                                            href={teacher.intro_video_url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            style={{
                                              color: theme.palette.primary.main,
                                              textDecoration: 'none'
                                            }}
                                          >
                                            {t('search.watchIntroVideo')}
                                          </a>
                                        </Typography>
                                      </Box>
                                    )}
                                  </Box>
                                </Box>
                              )}
                            </Box>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}

            {/* Pagination */}
            {!loading && teachers.length > 0 && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                <Pagination
                  count={totalPages}
                  page={page}
                  onChange={(e, value) => setPage(value)}
                  color="primary"
                  size={isMobile ? 'small' : 'medium'}
                />
              </Box>
            )}
          </Grid>
        </Grid>
        </ProfileCompletionAlert>
      </Container>
    </Layout>
  );
};

export default FindTeacher;
