import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardMedia,
  IconButton,
  useTheme,
  useMediaQuery,
  Paper,
  Fade,
  Slide,
  Divider,
  Avatar,
  Rating,
  Zoom,
  CircularProgress
} from '@mui/material';
import {
  Language as LanguageIcon,
  School,
  MenuBook,
  AccessTime,
  Group,
  Star,

  Timeline,
  LocalLibrary,
  ArrowForward as ArrowForwardIcon,
  ArrowBack as ArrowBackIcon,
  Language
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { alpha } from '@mui/material/styles';

const iconMap = {
  School: <School sx={{ fontSize: 40 }} />,
  MenuBook: <MenuBook sx={{ fontSize: 40 }} />,
  AccessTime: <AccessTime sx={{ fontSize: 40 }} />,
  Group: <Group sx={{ fontSize: 40 }} />
};

const Home = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isRtl = i18n.language === 'ar';
  const [activeTestimonial, setActiveTestimonial] = useState(0);
  const [loading, setLoading] = useState(true);
  const [homeData, setHomeData] = useState({
    subjects: [],
    testimonials: [],
    stats: {},
    teachers: [],
    features: []
  });

  useEffect(() => {
    const fetchHomeData = async () => {
      try {
        const response = await axios.get('/api/home/<USER>');
        if (response.data.success) {
          // Log received teacher data to verify profile pictures
          console.log('Teachers data in frontend:', response.data.data.teachers.map(t => ({
            name: t.full_name,
            profile_pic: t.profile_picture_url
          })));
          setHomeData(response.data.data);
        }
      } catch (error) {
        console.error('Error fetching home data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchHomeData();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveTestimonial((prev) => (prev + 1) % homeData.testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [homeData.testimonials.length]);

  const handleLanguageChange = () => {
    const newLang = i18n.language === 'en' ? 'ar' : 'en';
    i18n.changeLanguage(newLang);
    isRtl = newLang === 'ar';
    document.dir = newLang === 'ar' ? 'rtl' : 'ltr';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'relative', overflow: 'hidden' }}>
      {/* Hero Section */}
      <Box
        sx={{
          position: 'relative',
          color: 'white',
          pt: { xs: 8, md: 10 },
          pb: { xs: 10, md: 16 },
          minHeight: '70vh',
          display: 'flex',
          alignItems: 'center',
          backgroundImage: `url("https://modo3.com/thumbs/fit630x300/5996/1641114270/%D8%AA%D8%B1%D8%AA%D9%8A%D8%A8_%D8%A7%D9%84%D9%82%D8%B1%D8%A7%D9%86_%D8%A7%D9%84%D9%83%D8%B1%D9%8A%D9%85.jpg")`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: alpha(theme.palette.primary.dark, 0.85),
            zIndex: 1
          }
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          <Grid container spacing={4} alignItems="center" justifyContent="center">
            <Grid item xs={12} md={8} textAlign="center">
              <Fade in timeout={1000}>
                <Box>
                  <Typography
                    variant="h1"
                    sx={{
                      fontSize: { xs: '2.5rem', md: '3.5rem' },
                      fontWeight: 700,
                      mb: 2,
                      fontFamily: isRtl ? '"Noto Kufi Arabic", sans-serif' : 'inherit',
                      textAlign: 'center',
                      textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                    }}
                  >
                    {t('hero.title')}
                  </Typography>
                  <Typography
                    variant="h2"
                    sx={{
                      fontSize: { xs: '1.5rem', md: '2rem' },
                      mb: 4,
                      opacity: 0.9,
                      fontFamily: isRtl ? '"Noto Kufi Arabic", sans-serif' : 'inherit',
                      textAlign: 'center',
                      textShadow: '0 2px 4px rgba(0,0,0,0.2)'
                    }}
                  >
                    {t('hero.subtitle')}
                  </Typography>
                  <Box sx={{
                    display: 'flex',
                    gap: 2,
                    justifyContent: 'center',
                    flexWrap: 'wrap'
                  }}>
                    <Button
                      variant="contained"
                      color="secondary"
                      size="large"
                      endIcon={isRtl ? <ArrowBackIcon /> : <ArrowForwardIcon />}
                      onClick={() => navigate('/register/student')}
                      sx={{
                        borderRadius: 2,
                        px: 4,
                        py: 1.5,
                        fontSize: '1.1rem',
                        boxShadow: theme.shadows[4],
                        '&:hover': {
                          boxShadow: theme.shadows[8],
                          transform: 'translateY(-2px)'
                        },
                        transition: 'all 0.3s ease'
                      }}
                    >
                      {t('hero.startLearning')}
                    </Button>
                    <Button
                      variant="outlined"
                      color="inherit"
                      size="large"
                      onClick={() => navigate('/register/teacher')}
                      sx={{
                        borderRadius: 2,
                        px: 4,
                        py: 1.5,
                        fontSize: '1.1rem',
                        borderWidth: 2,
                        '&:hover': {
                          borderWidth: 2,
                          transform: 'translateY(-2px)',
                          bgcolor: alpha('#fff', 0.1)
                        },
                        transition: 'all 0.3s ease'
                      }}
                    >
                      {t('hero.becomeTeacher')}
                    </Button>
                  </Box>
                </Box>
              </Fade>
            </Grid>
          </Grid>
        </Container>

        {/* Decorative bottom wave */}
        <Box
          sx={{
            position: 'absolute',
            bottom: -2,
            left: 0,
            width: '100%',
            zIndex: 2,
            filter: 'drop-shadow(0 -1px 4px rgba(0,0,0,0.2))'
          }}
        >
          <svg
            viewBox="0 0 1440 120"
            fill="white"
            preserveAspectRatio="none"
            style={{
              display: 'block',
              width: '100%',
              height: '80px'
            }}
          >
            <path
              d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"
            />
          </svg>
        </Box>
      </Box>

      {/* Why Choose Us Section */}
      <Box sx={{ py: { xs: 8, md: 12 } }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: '2rem', md: '2.5rem' },
                fontWeight: 700,
                mb: 2,
                background: `linear-gradient(120deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              {t('home.whyChooseUs')}
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{
                maxWidth: '800px',
                mx: 'auto',
                px: 2,
                fontSize: { xs: '1rem', md: '1.1rem' }
              }}
            >
              {t('home.whyChooseUsSubtitle')}
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {[
              { icon: 'School', title: 'quality', desc: 'qualityDesc' },
              { icon: 'AccessTime', title: 'flexible', desc: 'flexibleDesc' },
              { icon: 'Group', title: 'interactive', desc: 'interactiveDesc' }
            ].map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Box
                  sx={{
                    height: '100%',
                    p: 3,
                    textAlign: 'center',
                    borderRadius: 4,
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      '& .feature-icon': {
                        transform: 'scale(1.1)',
                        color: 'primary.main',
                        bgcolor: alpha(theme.palette.primary.main, 0.15)
                      }
                    }
                  }}
                >
                  <Box
                    className="feature-icon"
                    sx={{
                      width: 80,
                      height: 80,
                      mx: 'auto',
                      mb: 3,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: '50%',
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                      color: 'primary.main',
                      transition: 'all 0.3s ease-in-out'
                    }}
                  >
                    {iconMap[feature.icon]}
                  </Box>
                  <Typography
                    variant="h6"
                    gutterBottom
                    sx={{ fontWeight: 600, mb: 1 }}
                  >
                    {t(`home.features.${feature.title}`)}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ fontSize: '0.95rem' }}
                  >
                    {t(`home.features.${feature.desc}`)}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Meet Our Teachers Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          bgcolor: alpha(theme.palette.primary.main, 0.03)
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: '2rem', md: '2.5rem' },
                fontWeight: 700,
                mb: 2,
                background: `linear-gradient(120deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              {t('home.meetTeachers')}
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{
                maxWidth: '800px',
                mx: 'auto',
                px: 2,
                fontSize: { xs: '1rem', md: '1.1rem' }
              }}
            >
              {t('home.meetTeachersSubtitle')}
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {homeData.teachers.map((teacher, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card
                  elevation={0}
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 4,
                    overflow: 'hidden',
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[8],
                      borderColor: 'transparent'
                    }
                  }}
                >
                  <Box
                    sx={{
                      p: 3,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      textAlign: 'center'
                    }}
                  >
                    <Avatar
                      src={teacher.profile_picture_url || undefined}
                      sx={{
                        width: 100,
                        height: 100,
                        mb: 2,
                        bgcolor: 'primary.main',
                        fontSize: '2.5rem'
                      }}
                    >
                      {!teacher.profile_picture_url && teacher.full_name?.charAt(0)}
                    </Avatar>
                    <Typography variant="h6" gutterBottom>
                      {teacher.full_name}
                    </Typography>
                    {teacher.cv && (
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          mb: 2,
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          height: '2.5em',
                          lineHeight: '1.25em'
                        }}
                      >
                        {teacher.cv}
                      </Typography>
                    )}
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      gutterBottom
                      sx={{ mb: 1 }}
                    >
                      {teacher.qualifications}
                    </Typography>
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        {t('teacher.nativeLanguage')}: {teacher.native_language}
                      </Typography>
                    </Box>
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        {t('teacher.teachingLanguages')}: {Array.isArray(teacher.teaching_languages)
                          ? teacher.teaching_languages.map(lang => lang.replace(/[\[\]"]/g, '')).join(', ')
                          : ''}
                      </Typography>
                    </Box>
                    <Typography
                      variant="body2"
                      color="text.primary"
                      sx={{
                        bgcolor: 'primary.light',
                        color: 'primary.contrastText',
                        px: 2,
                        py: 0.5,
                        borderRadius: 1,
                        mb: 1
                      }}
                    >
                      {teacher.subjects.split(',').join(' • ')}
                    </Typography>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Subjects Section */}
      <Box
        sx={{
          py: { xs: 8, md: 12 },
          background: `linear-gradient(180deg, ${alpha(theme.palette.background.default, 0)} 0%, ${alpha(theme.palette.primary.main, 0.03)} 100%)`
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: '2rem', md: '2.5rem' },
                fontWeight: 700,
                mb: 2,
                background: `linear-gradient(120deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textAlign: 'center'
              }}
            >
              {t('home.subjects')}
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{
                maxWidth: '800px',
                mx: 'auto',
                px: 2,
                fontSize: { xs: '1rem', md: '1.1rem' }
              }}
            >
              {t('home.subjectsSubtitle')}
            </Typography>
          </Box>

          <Grid container spacing={3}>
            {homeData.subjects.map((subject, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card
                  elevation={0}
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.3s ease-in-out',
                    borderRadius: 4,
                    overflow: 'hidden',
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[8],
                      borderColor: 'transparent',
                      '& .icon-wrapper': {
                        transform: 'scale(1.1)',
                        bgcolor: alpha(theme.palette.primary.main, 0.15)
                      }
                    },
                  }}
                >
                  <CardContent sx={{ p: 3, flexGrow: 1 }}>
                    <Box
                      className="icon-wrapper"
                      sx={{
                        display: 'inline-flex',
                        p: 2,
                        borderRadius: 3,
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: 'primary.main',
                        mb: 2.5,
                        transition: 'all 0.3s ease-in-out'
                      }}
                    >
                      {subject.icon && iconMap[subject.icon]}
                    </Box>

                    <Typography
                      variant="h5"
                      gutterBottom
                      sx={{
                        fontWeight: 600,
                        mb: 1.5,
                        fontSize: { xs: '1.25rem', md: '1.4rem' }
                      }}
                    >
                      {t(subject.name)}
                    </Typography>

                    <Typography
                      variant="body1"
                      color="text.secondary"
                      sx={{
                        mb: 3,
                        fontSize: { xs: '0.9rem', md: '1rem' },
                        minHeight: 75
                      }}
                    >
                      {t(subject.description)}
                    </Typography>

                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      mt: 'auto'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Group sx={{ mr: 1, color: 'primary.main', fontSize: 20 }} />
                        <Typography variant="body2" color="text.secondary">
                          {subject.teacherCount} {t('home.teachers')}
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Testimonials Section */}
      <Box sx={{ bgcolor: 'grey.50', py: 8 }}>
        <Container maxWidth="lg">
          <Typography
            variant="h2"
            align="center"
            gutterBottom
            sx={{
              mb: 2,
              fontWeight: 'bold',
              background: `linear-gradient(120deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}
          >
            {t('home.testimonials')}
          </Typography>
          <Typography
            variant="h6"
            color="text.secondary"
            align="center"
            sx={{
              maxWidth: '800px',
              mx: 'auto',
              px: 2,
              fontSize: { xs: '1rem', md: '1.1rem' },
              mb: 6
            }}
          >
            {t('home.testimonialsSubtitle') || 'ما يقوله طلابنا عن تجربتهم مع معلمينا'}
          </Typography>

          {homeData.testimonials.length === 0 ? (
            <Paper
              elevation={3}
              sx={{
                p: 4,
                textAlign: 'center',
                maxWidth: 800,
                mx: 'auto',
              }}
            >
              <Typography variant="body1" sx={{ mb: 2 }}>
                {t('home.noTestimonials') || 'لا توجد مراجعات حتى الآن. كن أول من يكتب مراجعة!'}
              </Typography>
            </Paper>
          ) : (
            <Box sx={{ position: 'relative' }}>
              {homeData.testimonials.map((testimonial, index) => (
                <Fade
                  key={index}
                  in={activeTestimonial === index}
                  timeout={500}
                  style={{
                    display: activeTestimonial === index ? 'block' : 'none',
                  }}
                >
                  <Paper
                    elevation={3}
                    sx={{
                      p: 4,
                      maxWidth: 800,
                      mx: 'auto',
                      borderRadius: 4,
                      position: 'relative'
                    }}
                  >
                    <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, alignItems: 'center', mb: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: { xs: 2, sm: 0 } }}>
                        <Avatar
                          src={testimonial.avatar || '/images/default-avatar.jpg'}
                          alt={testimonial.full_name}
                          sx={{
                            width: 70,
                            height: 70,
                            border: '3px solid',
                            borderColor: 'primary.main',
                            mr: 2
                          }}
                        />
                        <Box>
                          <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>
                            {testimonial.full_name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {t(testimonial.role)}
                          </Typography>
                          <Rating value={testimonial.rating} readOnly size="small" sx={{ mt: 0.5 }} />
                        </Box>
                      </Box>

                      {testimonial.teacher_name && (
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          ml: { xs: 0, sm: 'auto' },
                          mt: { xs: 2, sm: 0 }
                        }}>
                          <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                            {t('home.reviewFor') || 'مراجعة لـ:'}
                          </Typography>
                          <Avatar
                            src={testimonial.teacher_avatar || '/images/default-avatar.jpg'}
                            alt={testimonial.teacher_name}
                            sx={{
                              width: 40,
                              height: 40,
                              mr: 1
                            }}
                          />
                          <Typography variant="body2" fontWeight="medium">
                            {testimonial.teacher_name}
                          </Typography>
                        </Box>
                      )}
                    </Box>

                    <Divider sx={{ mb: 3 }} />

                    <Typography
                      variant="body1"
                      sx={{
                        fontSize: '1.1rem',
                        fontStyle: 'italic',
                        mb: 3,
                        textAlign: 'right',
                        direction: 'rtl'
                      }}
                    >
                      "{testimonial.text}"
                    </Typography>

                    {testimonial.created_at && (
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textAlign: 'right' }}>
                        {new Date(testimonial.created_at).toLocaleDateString('ar-EG', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </Typography>
                    )}
                  </Paper>
                </Fade>
              ))}

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  mt: 3,
                  gap: 2,
                }}
              >
                {homeData.testimonials.map((_, index) => (
                  <Box
                    key={index}
                    onClick={() => setActiveTestimonial(index)}
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      bgcolor: activeTestimonial === index ? 'primary.main' : 'grey.300',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                    }}
                  />
                ))}
              </Box>
            </Box>
          )}
        </Container>
      </Box>

      {/* Stats Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Grid container spacing={4} justifyContent="center">
          <Grid item xs={12} sm={4}>
            <Paper
              elevation={3}
              sx={{
                p: 3,
                textAlign: 'center',
                height: '100%',
                transition: 'transform 0.3s ease-in-out',
                '&:hover': { transform: 'translateY(-8px)' },
              }}
            >
              <School sx={{ fontSize: 40, color: 'primary.main', mb: 2 }} />
              <Typography variant="h4" gutterBottom>
                {homeData.stats.teacherCount}+
              </Typography>
              <Typography variant="h6">{t('home.expertTeachers')}</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Paper
              elevation={3}
              sx={{
                p: 3,
                textAlign: 'center',
                height: '100%',
                transition: 'transform 0.3s ease-in-out',
                '&:hover': { transform: 'translateY(-8px)' },
              }}
            >
              <Group sx={{ fontSize: 40, color: 'primary.main', mb: 2 }} />
              <Typography variant="h4" gutterBottom>
                {homeData.stats.studentCount}+
              </Typography>
              <Typography variant="h6">{t('home.activeStudents')}</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Paper
              elevation={3}
              sx={{
                p: 3,
                textAlign: 'center',
                height: '100%',
                transition: 'transform 0.3s ease-in-out',
                '&:hover': { transform: 'translateY(-8px)' },
              }}
            >
              <MenuBook sx={{ fontSize: 40, color: 'primary.main', mb: 2 }} />
              <Typography variant="h4" gutterBottom>
                {homeData.stats.courseCount}+
              </Typography>
              <Typography variant="h6">{t('home.courses')}</Typography>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default Home;
