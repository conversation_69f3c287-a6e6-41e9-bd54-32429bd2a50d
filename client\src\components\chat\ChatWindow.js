import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  TextField,
  IconButton,
  Avatar,
  CircularProgress,
  Paper,
  Divider,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button
} from '@mui/material';
import { 
  Send as SendIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';

const formatMessageTime = (timestamp) => {
  if (!timestamp) return 'غير معروف';
  
  try {
    let date;
    
    // Convert timestamp to Date object based on its type
    if (typeof timestamp === 'number') {
      // Unix timestamp (seconds)
      date = new Date(timestamp * 1000);
    } else if (typeof timestamp === 'string') {
      if (timestamp.includes('T') || timestamp.includes('Z')) {
        // ISO string or UTC string
        date = new Date(timestamp);
      } else {
        // MySQL datetime string - assume it's in UTC
        date = new Date(timestamp + 'Z');
      }
    } else {
      throw new Error('Invalid timestamp format');
    }

    // Validate date object
    if (isNaN(date.getTime())) {
      console.error('Invalid date object:', date, 'from timestamp:', timestamp);
      return 'غير معروف';
    }
    
    // Get local time components
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const timeStr = `${hours}:${minutes}`;
    
    // Get today's date at midnight in local timezone
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Get yesterday's date at midnight in local timezone
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    // Format date based on when it was sent
    if (date >= today) {
      return `اليوم ${timeStr}`; // Today at HH:MM
    } else if (date >= yesterday) {
      return `أمس ${timeStr}`; // Yesterday at HH:MM
    } else {
      // Format date as DD/MM/YYYY HH:MM
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year} ${timeStr}`;
    }
  } catch (error) {
    console.error('Error formatting date:', error, 'for timestamp:', timestamp);
    return 'غير معروف';
  }
};

const ChatWindow = ({ chat, messages = [], onSendMessage, onEditMessage, onDeleteMessage, loading, currentUser }) => {
  const { t } = useTranslation();
  const [newMessage, setNewMessage] = useState('');
  const [editingMessage, setEditingMessage] = useState(null);
  const [editText, setEditText] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const messagesEndRef = useRef(null);
  const [, forceUpdate] = useState();

  // Update times every minute
  useEffect(() => {
    const timer = setInterval(() => {
      forceUpdate({});
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = () => {
    if (!newMessage.trim()) return;
    onSendMessage(newMessage);
    setNewMessage('');
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleMenuClick = (event, message) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedMessage(message);
    console.log('Menu opened for message:', message.id);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDeleteClick = () => {
    if (!selectedMessage) {
      console.error('No message selected for deletion');
      return;
    }
    console.log('Delete clicked for message:', selectedMessage.id);
    handleMenuClose();
    setOpenDeleteDialog(true);
  };

  const handleDeleteConfirm = () => {
    if (!selectedMessage) {
      console.error('No message selected for deletion confirmation');
      return;
    }
    console.log('Delete confirmed for message:', selectedMessage.id);
    onDeleteMessage(selectedMessage.id);
    setOpenDeleteDialog(false);
    setSelectedMessage(null);
  };

  const handleDeleteCancel = () => {
    setOpenDeleteDialog(false);
    setSelectedMessage(null);
  };

  const handleEditClick = () => {
    if (!selectedMessage) {
      console.error('No message selected for editing');
      return;
    }
    console.log('Edit clicked for message:', selectedMessage.id);
    setEditingMessage(selectedMessage);
    handleMenuClose();
  };

  const handleEditCancel = () => {
    setEditingMessage(null);
    setEditText('');
  };

  const handleEditSave = () => {
    if (editText.trim() && editingMessage) {
      onEditMessage(editingMessage.id, editText.trim());
      setEditingMessage(null);
      setEditText('');
    }
  };

  if (!chat || !currentUser) {
    return (
      <Box sx={{ 
        height: '100%', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center',
        bgcolor: 'background.default'
      }}>
        <Typography variant="body1" color="textSecondary">
          {!chat ? t('chat.select_conversation') : t('chat.loading')}
        </Typography>
      </Box>
    );
  }

  // Determine if we're chatting with a student or teacher
  const otherUser = {
    name: chat.student_name || chat.teacher_name || t('chat.unknownUser'),
    picture: chat.student_picture || chat.teacher_picture || ''
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Chat Header */}
      <Box sx={{ 
        p: 2, 
        borderBottom: 1, 
        borderColor: 'divider', 
        display: 'flex', 
        alignItems: 'center',
        bgcolor: 'background.paper'
      }}>
        <Avatar 
          src={otherUser.picture} 
          sx={{ 
            mr: 2,
            width: 40,
            height: 40
          }}
        >
          {otherUser.name[0] || 'U'}
        </Avatar>
        <Box>
          <Typography variant="subtitle1" fontWeight="medium">
            {otherUser.name}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            {chat.status}
          </Typography>
        </Box>
      </Box>

      {/* Messages Area */}
      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
        {loading ? (
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%' 
          }}>
            <CircularProgress />
          </Box>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {messages.map((message) => (
              <Box
                key={message.id}
                sx={{
                  display: 'flex',
                  justifyContent: message.sender_id === currentUser.id ? 'flex-end' : 'flex-start',
                  mb: 2,
                  position: 'relative'
                }}
              >
                {message.sender_id !== currentUser.id && (
                  <Avatar
                    src={chat.teacher_picture || chat.student_picture}
                    sx={{ width: 32, height: 32, mr: 1 }}
                  />
                )}
                <Box sx={{ maxWidth: '70%', position: 'relative' }}>
                  <Paper
                    elevation={1}
                    sx={{
                      p: 2,
                      backgroundColor: message.sender_id === currentUser.id ? 'primary.main' : 'grey.100',
                      color: message.sender_id === currentUser.id ? 'white' : 'text.primary',
                      borderRadius: '12px',
                      position: 'relative',
                      minWidth: '150px'
                    }}
                  >
                    {editingMessage?.id === message.id ? (
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <TextField
                          fullWidth
                          multiline
                          size="small"
                          value={editText}
                          onChange={(e) => setEditText(e.target.value)}
                          sx={{ 
                            backgroundColor: 'white',
                            '& .MuiOutlinedInput-root': {
                              borderRadius: '8px'
                            }
                          }}
                        />
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                          <Button size="small" onClick={handleEditCancel}>{t('common.cancel')}</Button>
                          <Button size="small" variant="contained" onClick={handleEditSave}>{t('common.save')}</Button>
                        </Box>
                      </Box>
                    ) : (
                      <>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                          <Typography variant="body1" sx={{ flex: 1 }}>{message.content}</Typography>
                          {message.sender_id === currentUser.id && !editingMessage && (
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuClick(e, message)}
                              sx={{
                                padding: '2px',
                                color: message.sender_id === currentUser.id ? 'white' : 'action.active',
                                opacity: 0.7,
                                '&:hover': {
                                  opacity: 1
                                }
                              }}
                              aria-haspopup="true"
                              aria-controls={Boolean(anchorEl) ? 'message-menu' : undefined}
                            >
                              <MoreVertIcon fontSize="small" />
                            </IconButton>
                          )}
                        </Box>
                        <Typography variant="caption" sx={{ display: 'block', mt: 0.5, opacity: 0.7 }}>
                          {formatMessageTime(message.created_at)}
                        </Typography>
                      </>
                    )}
                  </Paper>
                </Box>
              </Box>
            ))}
            <div ref={messagesEndRef} />
          </Box>
        )}
      </Box>

      {/* Message Input */}
      <Box sx={{ 
        p: 2, 
        borderTop: 1, 
        borderColor: 'divider',
        bgcolor: 'background.paper'
      }}>
        <Box sx={{ 
          display: 'flex', 
          gap: 1,
          alignItems: 'flex-end'
        }}>
          <TextField
            fullWidth
            multiline
            maxRows={4}
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={t('chat.type_message')}
            variant="outlined"
            size="small"
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2
              }
            }}
          />
          <IconButton 
            color="primary" 
            onClick={handleSend}
            disabled={!newMessage.trim()}
            sx={{ 
              p: 1,
              bgcolor: 'primary.main',
              color: 'primary.contrastText',
              '&:hover': {
                bgcolor: 'primary.dark'
              },
              '&.Mui-disabled': {
                bgcolor: 'action.disabledBackground',
                color: 'action.disabled'
              }
            }}
          >
            <SendIcon />
          </IconButton>
        </Box>
      </Box>

      <Menu
        id="message-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <MenuItem onClick={handleEditClick}>
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          {t('chat.edit')}
        </MenuItem>
        <MenuItem onClick={handleDeleteClick} sx={{ color: 'error.main' }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          {t('chat.delete')}
        </MenuItem>
      </Menu>

      <Dialog
        open={openDeleteDialog}
        onClose={handleDeleteCancel}
        aria-labelledby="delete-dialog-title"
      >
        <DialogTitle id="delete-dialog-title">
          {t('chat.deleteConfirmTitle')}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {t('chat.deleteConfirmMessage')}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>
            {t('common.cancel')}
          </Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            variant="contained"
            autoFocus
          >
            {t('common.delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ChatWindow;
