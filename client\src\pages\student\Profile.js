import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import {
  Container,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Box,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip,
  Card,
  CardContent,
  Divider,
  FormControlLabel,
  Switch,
  CircularProgress
} from '@mui/material';
import {
  PhotoCamera as PhotoCameraIcon,
  School as SchoolIcon,
  Language as LanguageIcon,
  AccessTime as AccessTimeIcon,
  Person as PersonIcon,
  Translate as TranslateIcon,
  LocationOn as LocationIcon,
  Edit as EditIcon,
  Key as KeyIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout';
import ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';
import { useAuth } from '../../contexts/AuthContext';
import { timezones } from '../../utils/constants';

const Profile = () => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  const [formData, setFormData] = useState({
    full_name: currentUser?.full_name || '',
    email: currentUser?.email || '',
    gender: currentUser?.gender || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [profileData, setProfileData] = useState(null);
  const [profilePicture, setProfilePicture] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(
    currentUser?.profile_picture_url || ''
  );
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [passwordDialog, setPasswordDialog] = useState(false);
  const [profileEditDialog, setProfileEditDialog] = useState(false);
  const [loading, setLoading] = useState(false);

  // حالة لبيانات نموذج تعديل الملف الشخصي
  const [profileFormData, setProfileFormData] = useState({
    native_language: '',
    islam_learning_language: '',
    age: '',
    country: '',
    timezone: '',
    arabic_proficiency_level: 'beginner',
    private_tutoring_preference: false
  });

  useEffect(() => {
    // Debug log
    console.log('Current user data:', currentUser);

    if (currentUser) {
      setFormData(prevData => ({
        ...prevData,
        full_name: currentUser.full_name || '',
        email: currentUser.email || '',
        gender: currentUser.gender || '',
      }));
      // Debug log
      console.log('Profile picture URL:', currentUser.profile_picture_url);
      if (currentUser.profile_picture_url) {
        setPreviewUrl(currentUser.profile_picture_url);
      }
    }
  }, [currentUser]);

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get('/api/students/profile', {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data.success && response.data.profile) {
          const profile = response.data.profile;
          setProfileData(profile);

          // تعبئة نموذج التعديل ببيانات الملف الشخصي
          setProfileFormData({
            native_language: profile.native_language || '',
            islam_learning_language: profile.islam_learning_language || '',
            age: profile.age || '',
            country: profile.country || '',
            timezone: profile.timezone || '',
            arabic_proficiency_level: profile.arabic_proficiency_level || 'beginner',
            private_tutoring_preference: profile.private_tutoring_preference || false
          });
        }
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError(t('profile.errors.fetchError'));
      }
    };

    fetchProfileData();
  }, [t]);

  const handleProfilePictureChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setError(t('profile.errors.fileTooLarge'));
        return;
      }
      if (!file.type.startsWith('image/')) {
        setError(t('profile.errors.invalidFileType'));
        return;
      }
      setProfilePicture(file);
      setPreviewUrl(URL.createObjectURL(file));
      setError('');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // معالج تغيير بيانات نموذج الملف الشخصي
  const handleProfileFormChange = (e) => {
    const { name, value, checked, type } = e.target;
    setProfileFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      // Upload profile picture if changed
      if (profilePicture) {
        const formData = new FormData();
        formData.append('profilePicture', profilePicture);

        const uploadResponse = await axios.post('/api/users/upload-profile-picture', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          }
        });

        if (uploadResponse.data.profilePictureUrl) {
          setPreviewUrl(uploadResponse.data.profilePictureUrl);
        }
      }

      // Update basic profile info
      const updateResponse = await axios.put('/api/users/profile', {
        full_name: formData.full_name,
        email: formData.email,
        gender: formData.gender
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (updateResponse.data.success) {
        setSuccess(t('profile.updateSuccess'));
        setProfilePicture(null); // Clear the selected file
      }
    } catch (err) {
      console.error('Error updating profile:', err);
      setError(err.response?.data?.message || t('profile.errors.update'));
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async () => {
    if (formData.newPassword !== formData.confirmPassword) {
      setError(t('profile.errors.passwordMismatch'));
      return;
    }

    setError('');
    setSuccess('');
    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      const response = await axios.put('/api/users/password', {
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setSuccess(t('profile.passwordUpdateSuccess'));
        setPasswordDialog(false);
        setFormData(prev => ({
          ...prev,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        }));
      }
    } catch (err) {
      setError(err.response?.data?.message || t('profile.errors.passwordChange'));
    } finally {
      setLoading(false);
    }
  };

  // وظيفة حفظ تغييرات الملف الشخصي
  const handleSaveProfileChanges = async () => {
    setError('');
    setSuccess('');
    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await axios.put('/api/students/profile', {
        nativeLanguage: profileFormData.native_language,
        islamLearningLanguage: profileFormData.islam_learning_language,
        arabicLearningLanguage: profileFormData.islam_learning_language, // استخدام نفس اللغة للعربية والإسلام
        age: profileFormData.age,
        country: profileFormData.country,
        timezone: profileFormData.timezone,
        arabicProficiencyLevel: profileFormData.arabic_proficiency_level,
        privateTutoringPreference: profileFormData.private_tutoring_preference
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        // تحديث بيانات الملف الشخصي
        const updatedProfileResponse = await axios.get('/api/students/profile', {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (updatedProfileResponse.data.success && updatedProfileResponse.data.profile) {
          setProfileData(updatedProfileResponse.data.profile);
        }

        setSuccess(t('student.profile.updateSuccess'));
        setProfileEditDialog(false);
      }
    } catch (err) {
      console.error('Error updating profile:', err);
      setError(err.response?.data?.message || t('profile.errors.updateFailed'));
    } finally {
      setLoading(false);
    }
  };



  return (
    <Layout>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>

        {/* Basic Profile Info - Editable */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h5" gutterBottom>
                {t('profile.basicInfo')}
              </Typography>
            </Grid>

            {/* Profile Picture */}
            <Grid item xs={12} display="flex" justifyContent="center">
              <Box position="relative">
                <Avatar
                  src={previewUrl}
                  sx={{
                    width: 120,
                    height: 120,
                    mb: 2,
                    border: '2px solid',
                    borderColor: 'primary.main'
                  }}
                />
                <input
                  accept="image/*"
                  type="file"
                  id="profile-picture-input"
                  onChange={handleProfilePictureChange}
                  style={{ display: 'none' }}
                />
                <label htmlFor="profile-picture-input">
                  <IconButton
                    color="primary"
                    component="span"
                    sx={{
                      position: 'absolute',
                      bottom: 16,
                      right: -16,
                      backgroundColor: 'background.paper',
                      boxShadow: 1,
                      '&:hover': {
                        backgroundColor: 'background.paper',
                      }
                    }}
                  >
                    <PhotoCameraIcon />
                  </IconButton>
                </label>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('profile.fullName')}
                name="full_name"
                value={formData.full_name}
                onChange={handleChange}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('profile.email')}
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>{t('profile.gender')}</InputLabel>
                <Select
                  name="gender"
                  value={formData.gender}
                  onChange={handleChange}
                  label={t('profile.gender')}
                  required
                >
                  <MenuItem value="male">{t('profile.genders.male')}</MenuItem>
                  <MenuItem value="female">{t('profile.genders.female')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} sx={{ display: 'flex', alignItems: 'center' }}>
              <Button
                variant="outlined"
                startIcon={<KeyIcon />}
                onClick={() => setPasswordDialog(true)}
                sx={{ mr: 2 }}
              >
                {t('profile.changePassword')}
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                onClick={handleSubmit}
                disabled={loading}
              >
                {loading ? t('common.saving') : t('common.save')}
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Profile Details */}
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<EditIcon />}
                onClick={() => setProfileEditDialog(true)}
              >
                {t('profile.editInfo')}
              </Button>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {t('student.profile.languagePreferences')}
                </Typography>
                <List>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <LanguageIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={t('student.profile.nativeLanguage')}
                      secondary={profileData?.native_language || t('common.notSet')}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <TranslateIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={t('student.profile.islamLearningLanguage')}
                      secondary={profileData?.islam_learning_language || t('common.notSet')}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {t('student.profile.personalInfo')}
                </Typography>
                <List>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <PersonIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={t('student.profile.age')}
                      secondary={profileData?.age || t('common.notSet')}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <LocationIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={t('student.profile.country')}
                      secondary={profileData?.country || t('common.notSet')}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar>
                        <AccessTimeIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={t('student.profile.timezone')}
                      secondary={profileData?.timezone || t('common.notSet')}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {t('student.profile.learningPreferences')}
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
                      <Typography variant="subtitle1" gutterBottom>
                        {t('student.profile.arabicLevel')}
                      </Typography>
                      <Chip
                        icon={<SchoolIcon />}
                        label={profileData?.arabic_proficiency_level || t('common.notSet')}
                        color="primary"
                        variant="outlined"
                        sx={{ mt: 1 }}
                      />
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
                      <Typography variant="subtitle1" gutterBottom>
                        {t('student.profile.privateTutoring')}
                      </Typography>
                      <Chip
                        icon={<PersonIcon />}
                        label={profileData?.private_tutoring_preference ?
                          t('student.profile.preferPrivate') :
                          t('student.profile.preferGroup')}
                        color={profileData?.private_tutoring_preference ? 'success' : 'info'}
                        variant="outlined"
                        sx={{ mt: 1 }}
                      />
                    </Paper>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* نافذة تعديل الملف الشخصي */}
        <Dialog
          open={profileEditDialog}
          onClose={() => setProfileEditDialog(false)}
          fullWidth
          maxWidth="md"
        >
          <DialogTitle>
            {t('profile.editInfo')}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={3} sx={{ mt: 0 }}>
              {/* تفضيلات اللغة */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  {t('student.profile.languagePreferences')}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('student.profile.nativeLanguage')}
                  name="native_language"
                  value={profileFormData.native_language}
                  onChange={handleProfileFormChange}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('student.profile.islamLearningLanguage')}
                  name="islam_learning_language"
                  value={profileFormData.islam_learning_language}
                  onChange={handleProfileFormChange}
                  required
                />
              </Grid>

              {/* المعلومات الشخصية */}
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>
                  {t('student.profile.personalInfo')}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  type="number"
                  label={t('student.profile.age')}
                  name="age"
                  value={profileFormData.age}
                  onChange={handleProfileFormChange}
                  required
                  inputProps={{ min: 5, max: 120 }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('student.profile.country')}
                  name="country"
                  value={profileFormData.country}
                  onChange={handleProfileFormChange}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>{t('student.profile.timezone')}</InputLabel>
                  <Select
                    name="timezone"
                    value={profileFormData.timezone}
                    onChange={handleProfileFormChange}
                    required
                    label={t('student.profile.timezone')}
                  >
                    {timezones.map((timezone) => (
                      <MenuItem key={timezone.value} value={timezone.value}>
                        {timezone.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* تفضيلات التعلم */}
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>
                  {t('student.profile.learningPreferences')}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>{t('student.profile.arabicLevel')}</InputLabel>
                  <Select
                    name="arabic_proficiency_level"
                    value={profileFormData.arabic_proficiency_level}
                    onChange={handleProfileFormChange}
                    required
                    label={t('student.profile.arabicLevel')}
                  >
                    <MenuItem value="beginner">{t('student.profile.levels.beginner')}</MenuItem>
                    <MenuItem value="intermediate">{t('student.profile.levels.intermediate')}</MenuItem>
                    <MenuItem value="advanced">{t('student.profile.levels.advanced')}</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={profileFormData.private_tutoring_preference}
                      onChange={handleProfileFormChange}
                      name="private_tutoring_preference"
                      color="primary"
                    />
                  }
                  label={t('student.profile.privateTutoring')}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setProfileEditDialog(false)}>
              {t('common.cancel')}
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSaveProfileChanges}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
            >
              {loading ? t('common.saving') : t('common.save')}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Password Change Dialog */}
        <Dialog open={passwordDialog} onClose={() => setPasswordDialog(false)}>
          <DialogTitle>{t('profile.changePassword')}</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ pt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  type="password"
                  label={t('profile.currentPassword')}
                  name="currentPassword"
                  value={formData.currentPassword}
                  onChange={handleChange}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  type="password"
                  label={t('profile.newPassword')}
                  name="newPassword"
                  value={formData.newPassword}
                  onChange={handleChange}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  type="password"
                  label={t('profile.confirmPassword')}
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  required
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPasswordDialog(false)}>
              {t('common.cancel')}
            </Button>
            <Button
              onClick={handlePasswordChange}
              variant="contained"
              color="primary"
              disabled={loading}
            >
              {loading ? t('common.saving') : t('common.save')}
            </Button>
          </DialogActions>
        </Dialog>
        </ProfileCompletionAlert>
      </Container>
    </Layout>
  );
};

export default Profile;
