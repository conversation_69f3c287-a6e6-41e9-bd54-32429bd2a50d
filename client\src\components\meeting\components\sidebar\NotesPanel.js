import React, { useEffect, useRef, useState } from "react";
import { useAuth } from "../../../../contexts/AuthContext";
import { debounce } from "../../utils/common";
import axios from "../../../../utils/axios";
export default function NotesPanel({ panelHeight, teacherId, studentId, meetingId }) {
  console.log('NotesPanel IDs:', { teacherId, studentId, meetingId });
  const inputHeight = 56;
  const listHeight = panelHeight - inputHeight;
  const [content, setContent] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(true);
  const { currentUser } = useAuth();
  const textareaRef = useRef();

  // fetch existing note
  useEffect(() => {
    if (!teacherId || !studentId) return;
    const load = async () => {
      try {
        const params = { teacherId: Number(teacherId), studentId: Number(studentId) };
        if (meetingId) params.meetingId = meetingId;
        const { data } = await axios.get("/notes", { params });
        const note = data.content || "";
        setContent(note);
        setIsEditing(note ? false : true);
      } catch (err) {
        console.error("fetch notes", err);
        setContent("");
      }
    };
    load();
  }, [teacherId, studentId]);

  // auto save, debounced
  const saveNote = async (val) => {
    try {
      if (!teacherId || !studentId) return;
      const payload = {
        teacherId: Number(teacherId),
        studentId: Number(studentId),
        content: val,
        writtenBy: currentUser?.role === 'teacher' || currentUser?.role === 'platform_teacher' || currentUser?.role === 'new_teacher' ? 'teacher' : 'student',
      };
      if (meetingId) payload.meetingId = meetingId;
      await axios.post("/notes", payload);
    } catch (err) {
      console.error("save notes", err);
    }
  };
  const debouncedSave = useRef(debounce(async (val)=>{await saveNote(val); setSaved(true);}, 1500)).current;

  return (
    <div className="h-full flex flex-col">
      {isEditing ? (
        <>
          {/* Edit mode controls (TOP) */}
          <div
            className="flex items-center justify-end bg-gray-800 px-4"
            style={{ height: inputHeight }}
          >
            <button
              disabled={saving}
              onClick={async () => {
                setSaving(true);
                await saveNote(content);
                setSaving(false);
                setSaved(true);
                setIsEditing(false);
              }}
              className={`px-4 py-2 text-white rounded-md ${saved || saving ? "bg-gray-500" : "bg-green-600 hover:bg-green-700"}`}
            >
              {saving ? "Saving..." : "Save"}
            </button>
            <button
              className="ml-2 px-3 py-2 rounded-md bg-gray-600 text-white hover:bg-gray-700"
              onClick={() => {
                setIsEditing(false);
              }}
            >
              Cancel
            </button>
          </div>
          {/* Edit mode textarea */}
          <textarea
            ref={textareaRef}
            className="flex-1 bg-gray-700 text-white p-4 resize-none outline-none"
            style={{ height: listHeight }}
            value={content}
            placeholder="Type notes here..."
            onChange={(e) => {
              const val = e.target.value;
              setContent(val);
              setSaved(false);
              debouncedSave(val);
            }}
          />
        </>
      ) : (
        <>
          {/* View mode controls (TOP) */}
          <div
            className="flex items-center justify-end bg-gray-800 px-4"
            style={{ height: inputHeight }}
          >
            <button
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md"
              onClick={() => {
                setIsEditing(true);
                setSaved(false);
              }}
            >
              {content ? "Edit Note" : "Add Note"}
            </button>
          </div>
          {/* View mode note display */}
          <div
            style={{ height: listHeight, overflowY: "auto" }}
            className="p-4 whitespace-pre-wrap break-words text-white bg-gray-700 flex-1"
          >
            {content || "No note yet"}
          </div>
        </>
      )}
    </div>
  );
}
