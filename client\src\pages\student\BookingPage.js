import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  Button,
  CircularProgress,
  Alert,
  Chip,
  Card,
  CardContent,
  CardActions,
  Divider,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  useMediaQuery,
  IconButton,
  Tooltip,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormControl,
  FormLabel
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  CalendarMonth as CalendarMonthIcon,
  AccessTime as AccessTimeIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout';
import BookableHoursTable from '../../components/BookableHoursTable';
import ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';
import { format, addDays, startOfWeek, isSameDay, addWeeks, subWeeks } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { convertBookingDateTime, getCurrentTimeInTimezone, formatDateInStudentTimezone, convertFromDatabaseTime, parseTimezoneOffset } from '../../utils/timezone';
import moment from 'moment-timezone';
import { toast } from 'react-hot-toast';

const BookingPage = () => {
  const { id } = useParams();
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentUser, token } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isRtl = i18n.language === 'ar';

  const [teacher, setTeacher] = useState(null);
  const [isTrialEligible, setIsTrialEligible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  
  const [bookingError, setBookingError] = useState(null);
  const [bookingData, setBookingData] = useState(null);
  const [currentWeekStart, setCurrentWeekStart] = useState(() => {
    const today = new Date();
    return startOfWeek(today, { weekStartsOn: 1 }); // Start from current week
  });
  const [lessonDuration, setLessonDuration] = useState('25'); // Default to half lesson (25 minutes)
  const [studentProfile, setStudentProfile] = useState(null);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Days of the week
  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

  // Week navigation functions
  const goToPreviousWeek = () => {
    const previousWeek = subWeeks(currentWeekStart, 1);
    const today = new Date();
    const lastWeek = startOfWeek(subWeeks(today, 1), { weekStartsOn: 1 });

    if (previousWeek >= lastWeek) {
      setCurrentWeekStart(previousWeek);
    }
  };

  const goToNextWeek = () => {
    const nextWeek = addWeeks(currentWeekStart, 1);
    const today = new Date();
    const oneYearAhead = addWeeks(today, 52); // One year ahead from today
    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });

    // Don't allow going beyond one year ahead
    if (nextWeek <= maxWeek) {
      setCurrentWeekStart(nextWeek);
    }
  };

  // Check if navigation buttons should be disabled
  const isPreviousWeekDisabled = () => {
    const previousWeek = subWeeks(currentWeekStart, 1);
    const today = new Date();
    const lastWeek = startOfWeek(subWeeks(today, 1), { weekStartsOn: 1 });
    return previousWeek < lastWeek;
  };

  const isNextWeekDisabled = () => {
    const nextWeek = addWeeks(currentWeekStart, 1);
    const today = new Date();
    const oneYearAhead = addWeeks(today, 52); // One year ahead from today
    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });
    return nextWeek > maxWeek;
  };

  // Fetch student profile
  useEffect(() => {
    const fetchStudentProfile = async () => {
      if (!token) return;

      try {
        const { data } = await axios.get('/api/students/profile', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (data.success && data.profile) {
          setStudentProfile(data.profile);
        }
      } catch (error) {
        console.error('Error fetching student profile:', error);
      }
    };

    fetchStudentProfile();
  }, [token]);

  // Fetch teacher details
  useEffect(() => {
    const fetchTeacherDetails = async () => {
      if (!id || !token) return;

      try {
        setLoading(true);
        const { data } = await axios.get(`/teachers/${id}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (data.success) {
          setTeacher(data.data);
      // After teacher details load, check if this is the first booking between student & teacher
      try {
        const hasBookedRes = await axios.get(`/api/bookings/student/hasBooked/${id}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (hasBookedRes.data.success) {
          setIsTrialEligible(!hasBookedRes.data.hasPrevious);
        }
      } catch(e) {
        console.error('Error checking previous bookings', e);
      }
        } else {
          setError(data.message || t('teacherDetails.errorFetching'));
        }
      } catch (error) {
        console.error('Error fetching teacher details:', error);
        setError(t('teacherDetails.errorFetching'));
      } finally {
        setLoading(false);
      }
    };

    fetchTeacherDetails();
  }, [id, token, t]);

  // Update current time every second
  useEffect(() => {
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timeInterval);
  }, []);

  // Get current time in student's timezone (same method as meetings page)
  const getCurrentTimeInStudentTimezone = () => {
    if (!studentProfile || !studentProfile.timezone) {
      return new Date();
    }
    // Use the same method as the time display at the top
    const currentTimeFormatted = formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
    return moment(currentTimeFormatted, 'YYYY-MM-DD HH:mm:ss').toDate();
  };

  // Check if a time slot is in the past
  const isSlotInPast = (day, timeSlotKey) => {
    const date = addDays(currentWeekStart, daysOfWeek.indexOf(day));
    const [startTime] = timeSlotKey.split('-');
    const [hours, minutes] = startTime.split(':').map(Number);

    // Create slot datetime in student's timezone
    const dateStr = format(date, 'yyyy-MM-dd');
    const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

    // Create the slot datetime and current time using the same method as the time display
    let slotDateTime;
    let now;

    if (studentProfile && studentProfile.timezone) {
      // Create slot datetime in student's timezone using moment-timezone
      const slotDateTimeStr = `${dateStr} ${timeStr}:00`;

      // Parse the slot time as if it's in the student's timezone
      const offsetMinutes = parseTimezoneOffset(studentProfile.timezone);
      const offsetHours = offsetMinutes / 60;
      const sign = offsetHours >= 0 ? '+' : '-';
      const absHours = Math.abs(offsetHours);
      const hours_offset = Math.floor(absHours);
      const minutes_offset = Math.round((absHours - hours_offset) * 60);
      const momentTimezone = `${sign}${String(hours_offset).padStart(2, '0')}:${String(minutes_offset).padStart(2, '0')}`;

      // Create slot datetime in student's timezone
      const slotMoment = moment(slotDateTimeStr, 'YYYY-MM-DD HH:mm:ss').utcOffset(momentTimezone);
      slotDateTime = slotMoment.toDate();

      // Get current time in student's timezone using the same method as display
      const currentTimeFormatted = formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
      now = moment(currentTimeFormatted, 'YYYY-MM-DD HH:mm:ss').toDate();
    } else {
      // Fallback to local time
      slotDateTime = new Date(date);
      slotDateTime.setHours(hours, minutes, 0, 0);
      now = new Date();
    }

    const isPast = slotDateTime < now;

    // Debug logging for first few slots
    if (Math.random() < 0.1) { // Log 10% of checks to avoid spam
      console.log('Slot time check:', {
        day,
        timeSlotKey,
        slotDateTime: slotDateTime.toString(),
        currentTime: now.toString(),
        timezone: studentProfile?.timezone,
        isPast,
        slotDateTimeStr: studentProfile?.timezone ? `${dateStr} ${timeStr}:00` : 'N/A',
        currentTimeFormatted: studentProfile?.timezone ? formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss') : 'N/A'
      });
    }

    return isPast;
  };

  // Handle slot selection
  const handleSelectSlot = (day, timeSlot) => {
    const date = addDays(currentWeekStart, daysOfWeek.indexOf(day));

    setSelectedSlot({
      day,
      timeSlot,
      date,
      formattedDate: format(date, 'yyyy-MM-dd'),
      formattedTime: timeSlot
    });

    setConfirmDialogOpen(true);
  };

  // Get consecutive hour blocks for a day
  const getConsecutiveHourBlocks = (daySlots) => {
    if (!daySlots || daySlots.length === 0) return [];

    const blocks = [];
    const sortedSlots = [...daySlots].sort();

    for (let i = 0; i < sortedSlots.length; i++) {
      const currentSlot = sortedSlots[i];
      const [startTime] = currentSlot.split('-');
      const [hours, minutes] = startTime.split(':').map(Number);

      // Only process slots that start at :00 (beginning of hour)
      if (minutes === 0) {
        // Check if the next 30-minute slot is also available
        let nextSlot;
        if (hours === 23) {
          // Special case for 23:00 - next slot is 23:30-00:00
          nextSlot = `23:30-00:00`;
        } else {
          nextSlot = `${hours.toString().padStart(2, '0')}:30-${(hours + 1).toString().padStart(2, '0')}:00`;
        }

        if (sortedSlots.includes(nextSlot)) {
          // This is a full hour block
          blocks.push({
            type: 'full',
            startSlot: currentSlot,
            endSlot: nextSlot,
            hour: hours
          });
          // Skip the next slot since it's part of this full hour
          i++;
        } else {
          // This is just a half hour
          blocks.push({
            type: 'half',
            slot: currentSlot,
            hour: hours,
            minute: minutes
          });
        }
      } else {
        // This is a :30 slot, check if it's not part of a full hour we already processed
        const prevSlot = `${hours.toString().padStart(2, '0')}:00-${hours.toString().padStart(2, '0')}:30`;
        if (!sortedSlots.includes(prevSlot)) {
          // This is just a half hour
          blocks.push({
            type: 'half',
            slot: currentSlot,
            hour: hours,
            minute: minutes
          });
        }
      }
    }

    return blocks;
  };

  // Check if a full hour is available for the selected slot
  const isFullHourAvailable = (day, timeSlotKey, availableHours) => {
    if (!availableHours || !availableHours[day]) return false;

    const blocks = getConsecutiveHourBlocks(availableHours[day]);
    const fullHourBlock = blocks.find(block =>
      block.type === 'full' && (block.startSlot === timeSlotKey || block.endSlot === timeSlotKey)
    );

    if (!fullHourBlock) return false;

    // Check if both slots are available (not in the past)
    const firstSlotPast = isSlotInPast(day, fullHourBlock.startSlot);
    const secondSlotPast = isSlotInPast(day, fullHourBlock.endSlot);

    // Full hour is only available if both slots are not in the past
    return !firstSlotPast && !secondSlotPast;
  };

  // Check if cross-hour booking is available
  const checkCrossHourAvailability = (day, timeSlotKey, availableHours) => {
    if (!availableHours || !availableHours[day]) return false;

    const [startTime] = timeSlotKey.split('-');
    const [hours, minutes] = startTime.split(':').map(Number);

    // Cross-hour booking is only possible if this is a :30 slot
    if (minutes !== 30) return false;

    // Check if the next hour's first half is available
    const nextHourFirstHalf = `${((hours + 1) % 24).toString().padStart(2, '0')}:00-${((hours + 1) % 24).toString().padStart(2, '0')}:30`;

    // Check if next hour slot exists in available hours
    if (!availableHours[day].includes(nextHourFirstHalf)) return false;

    // Check if both slots are not in the past
    const currentSlotPast = isSlotInPast(day, timeSlotKey);
    const nextSlotPast = isSlotInPast(day, nextHourFirstHalf);

    return !currentSlotPast && !nextSlotPast;
  };

  // Check if second half of hour is available for booking with flexible options
  const checkSecondHalfAvailability = (day, timeSlotKey, availableHours) => {
    if (!availableHours || !availableHours[day]) return false;

    const [startTime] = timeSlotKey.split('-');
    const [hours, minutes] = startTime.split(':').map(Number);

    // For :00 slots, check if the second half (:30) is available
    if (minutes === 0) {
      const secondHalfSlot = `${hours.toString().padStart(2, '0')}:30-${((hours + 1) % 24).toString().padStart(2, '0')}:00`;

      // Check if second half slot exists and is not in the past
      if (availableHours[day].includes(secondHalfSlot)) {
        const secondHalfPast = isSlotInPast(day, secondHalfSlot);
        if (!secondHalfPast) {
          // Check if the slot after the second half is also available (for full lesson option)
          const nextHourFirstHalf = `${((hours + 1) % 24).toString().padStart(2, '0')}:00-${((hours + 1) % 24).toString().padStart(2, '0')}:30`;
          const hasNextSlot = availableHours[day].includes(nextHourFirstHalf) && !isSlotInPast(day, nextHourFirstHalf);

          return {
            canBookSecondHalf: true,
            canBookFullFromSecondHalf: hasNextSlot
          };
        }
      }
    }

    // For :30 slots, check if the next hour's first half is available (for full lesson option)
    if (minutes === 30) {
      const nextHourFirstHalf = `${((hours + 1) % 24).toString().padStart(2, '0')}:00-${((hours + 1) % 24).toString().padStart(2, '0')}:30`;
      const hasNextSlot = availableHours[day].includes(nextHourFirstHalf) && !isSlotInPast(day, nextHourFirstHalf);

      return {
        canBookSecondHalf: true,
        canBookFullFromSecondHalf: hasNextSlot
      };
    }

    return {
      canBookSecondHalf: false,
      canBookFullFromSecondHalf: false
    };
  };



  // Check if full hour lesson is possible for selected slot (supports cross-day)
  const canBookFullHour = (day, timeSlotKey) => {
    if (!teacher?.available_hours) return false;

    try {
      const availableHours = typeof teacher.available_hours === 'string'
        ? JSON.parse(teacher.available_hours)
        : teacher.available_hours;

      // Extract hour/minute of the chosen slot
      const [startTime] = timeSlotKey.split('-');
      const [hours, minutes] = startTime.split(':').map(Number);

      // Helper – make sure the given slot is available on the given day and not in the past
      const isSlotAvailableAndFuture = (checkDay, slotKey) => {
        const slots = availableHours[checkDay] || [];
        return slots.includes(slotKey) && !isSlotInPast(checkDay, slotKey);
      };

      // If user picked the first half of an hour (:00-:30), we only need the second half on the SAME day
      if (minutes === 0) {
        const secondHalfSlot = `${hours.toString().padStart(2, '0')}:30-${((hours + 1) % 24).toString().padStart(2, '0')}:00`;
        return isSlotAvailableAndFuture(day, secondHalfSlot);
      }

      // If user picked the second half of an hour (:30-:00), we need the next hour's first half
      if (minutes === 30) {
        // Calculate next hour and whether this crosses to the next calendar day
        const nextHour = (hours + 1) % 24;
        const nextHourStr = nextHour.toString().padStart(2, '0');
        const nextHourFirstHalf = `${nextHourStr}:00-${nextHourStr}:30`;

        const currentDayIndex = daysOfWeek.indexOf(day);
        // When the selected slot is 23:30-00:00 we roll over to the following day
        const nextDayName = hours === 23
          ? daysOfWeek[(currentDayIndex + 1) % daysOfWeek.length]
          : day;

        return isSlotAvailableAndFuture(nextDayName, nextHourFirstHalf);
      }

      return false;
    } catch (error) {
      console.error('Error checking full hour availability:', error);
      return false;
    }
  };

  // Handle slot selection from table
  const handleTableSlotSelect = (day, timeSlotKey) => {
    // Check if slot is in the past
    if (isSlotInPast(day, timeSlotKey)) {
      return; // Don't allow booking past slots
    }

    const date = addDays(currentWeekStart, daysOfWeek.indexOf(day));

    setSelectedSlot({
      day,
      timeSlot: timeSlotKey,
      date,
      formattedDate: format(date, 'yyyy-MM-dd')
    });

    // Since we removed full hour buttons, all slots are now 30-minute slots
    // Default to 25 minutes (half lesson)
    // Default to 25 minutes; for trial it is mandatory, for others still default but can change
  setLessonDuration('25');

    setConfirmDialogOpen(true);
  };

  // Handle booking confirmation
  const handleConfirmBooking = async () => {
    if (!selectedSlot || !currentUser || !token) return;

    try {
      const [startTime] = selectedSlot.timeSlot.split('-');

      // Create booking datetime by removing student's timezone offset
      let bookingDateTime;
      if (studentProfile && studentProfile.timezone) {
        // The selected time is already in student's timezone, we need to convert it back to base time
        // Create the datetime in student's timezone
        const studentDateTime = moment.tz(
          `${selectedSlot.formattedDate} ${startTime}:00`,
          'YYYY-MM-DD HH:mm:ss',
          studentProfile.timezone
        );

        // Convert back to base time (remove timezone offset) for database storage
        bookingDateTime = studentDateTime.clone().subtract(parseTimezoneOffset(studentProfile.timezone), 'minutes').format('YYYY-MM-DD HH:mm:ss');
      } else {
        // Fallback to original behavior if no timezone info
        bookingDateTime = `${selectedSlot.formattedDate} ${startTime}:00`;
      }

      console.log('Sending booking request:', {
        duration: lessonDuration,
        timezone: studentProfile?.timezone,
        originalDateTime: `${selectedSlot.formattedDate} ${startTime}:00`,
        convertedDateTime: bookingDateTime
      });

      const { data } = await axios.post('/bookings', {
        teacher_id: id,
        datetime: bookingDateTime,
        duration: lessonDuration,
        booking_type: 'regular'
      });

      if (data.success) {
        
        setBookingData(data.data);
      // Show persistent success toast
      toast.success(t('booking.bookingSuccessMessage'), { duration: 8000 });
        setConfirmDialogOpen(false);

        // Fetch the meeting details for this booking
        try {
          const meetingsResponse = await axios.get('/meetings/student');
          if (meetingsResponse.data && Array.isArray(meetingsResponse.data.meetings)) {
            // Find the meeting that matches this booking's datetime
            const relatedMeeting = meetingsResponse.data.meetings.find(
              meeting => new Date(meeting.meeting_date).getTime() === new Date(bookingDateTime).getTime()
            );

            if (relatedMeeting) {
              console.log('Found related meeting:', relatedMeeting);
              setBookingData(prevData => ({
                ...prevData,
                meeting: relatedMeeting
              }));
            }
          }
        } catch (meetingError) {
          console.error('Error fetching meeting details:', meetingError);
        }
      } else {
        setBookingError(data.message || t('booking.bookingFailed'));
      }
    } catch (error) {
      console.error('Error booking slot:', error);
      if (error.response?.data?.message === 'Insufficient balance. Please add funds to your wallet.') {
        setBookingError(t('booking.insufficientBalance'));
      } else {
        setBookingError(error.response?.data?.message || t('booking.bookingFailed'));
      }
    }
  };

  // Generate weekly calendar
  const renderWeeklyCalendar = () => {
    if (!teacher || !teacher.available_hours) return null;

    return (
      <Grid container spacing={2}>
        {daysOfWeek.map((day, index) => {
          const date = addDays(currentWeekStart, index);
          const dayHours = teacher.available_hours[day] || [];

          return (
            <Grid item xs={12} sm={6} md={4} lg={3} key={day}>
              <Card elevation={3} sx={{ height: '100%' }}>
                <CardContent>
                  <Box sx={{
                    textAlign: 'center',
                    pb: 2,
                    borderBottom: 1,
                    borderColor: 'divider',
                    bgcolor: theme.palette.primary.main,
                    color: 'white',
                    py: 1,
                    borderRadius: '4px 4px 0 0',
                    mb: 2
                  }}>
                    <Typography variant="h6">
                      {t(`days.${day}`)}
                    </Typography>
                    <Typography variant="body2">
                      {format(date, 'MMM d, yyyy', { locale: isRtl ? ar : enUS })}
                    </Typography>
                  </Box>

                  {dayHours.length > 0 ? (
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {dayHours.map((timeSlot, idx) => (
                        <Button
                          key={idx}
                          variant="outlined"
                          color="primary"
                          onClick={() => handleSelectSlot(day, timeSlot)}
                          startIcon={<AccessTimeIcon />}
                          sx={{ justifyContent: 'flex-start' }}
                        >
                          {timeSlot}
                        </Button>
                      ))}
                    </Box>
                  ) : (
                    <Box sx={{ textAlign: 'center', py: 3 }}>
                      <Typography variant="body2" color="text.secondary">
                        {t('booking.noAvailableSlots')}
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>
    );
  };

  // Render booking success message
  const renderBookingSuccess = () => (
    <Paper elevation={3} sx={{ p: 4, textAlign: 'center', my: 4 }}>
      <CheckCircleIcon color="success" sx={{ fontSize: 60, mb: 2 }} />
      <Typography variant="h5" gutterBottom>
        {t('booking.bookingSuccessTitle')}
      </Typography>
      <Typography variant="body1" paragraph>
        {t('booking.bookingSuccessMessage')}
      </Typography>

      {bookingData?.meeting && (
        <Box sx={{ mt: 3, mb: 3, p: 3, bgcolor: 'background.paper', borderRadius: 2, boxShadow: 1 }}>
          <Typography variant="h6" gutterBottom color="primary">
            {t('booking.meetingCreated')}
          </Typography>
          <Typography variant="body1" gutterBottom>
            <strong>{t('meetings.name')}:</strong> {bookingData.meeting.meeting_name}
          </Typography>
          <Typography variant="body1" gutterBottom>
            <strong>{t('meetings.date')}:</strong> {format(new Date(bookingData.meeting.meeting_date), 'PPpp', { locale: isRtl ? ar : enUS })}
          </Typography>
          <Typography variant="body1" gutterBottom>
            <strong>{t('meetings.duration')}:</strong> {bookingData.meeting.duration} {t('meetings.minutes')}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            {t('booking.meetingAccessInfo')}
          </Typography>
        </Box>
      )}

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>
        <Button
          variant="outlined"
          component={Link}
          to={`/student/teacher/${id}`}
          startIcon={<ArrowBackIcon />}
        >
          {t('booking.backToTeacher')}
        </Button>
        <Button
          variant="contained"
          component={Link}
          to="/student/meetings"
          color="primary"
        >
          {t('booking.viewMyMeetings')}
        </Button>
        <Button
          variant="outlined"
          component={Link}
          to="/student/bookings"
          color="secondary"
        >
          {t('booking.viewMyBookings')}
        </Button>
      </Box>
    </Paper>
  );

  // Confirmation dialog
  const renderConfirmDialog = () => (
    <Dialog open={confirmDialogOpen} onClose={() => setConfirmDialogOpen(false)}>
      <DialogTitle>
        {t('booking.confirmBooking')}
        <IconButton
          aria-label="close"
          onClick={() => setConfirmDialogOpen(false)}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        {selectedSlot && (
          <Box sx={{ py: 2 }}>
            <Typography variant="body1" gutterBottom>
              <strong>{t('booking.teacher')}:</strong> {teacher?.full_name}
            </Typography>
            <Typography variant="body1" gutterBottom>
              <strong>{t('booking.day')}:</strong> {t(`days.${selectedSlot.day}`)}
            </Typography>
            <Typography variant="body1" gutterBottom>
              <strong>{t('booking.date')}:</strong> {format(selectedSlot.date, 'PPP', { locale: isRtl ? ar : enUS })}
            </Typography>
            <Typography variant="body1" gutterBottom>
              <strong>{t('booking.time')}:</strong> {(() => {
                if (lessonDuration === '50' && selectedSlot) {
                  // For full hour lessons, show full 60-minute time range from selected slot
                  const [startTime] = selectedSlot.timeSlot.split('-');
                  const [hours, minutes] = startTime.split(':').map(Number);

                  // Calculate the end time for full hour (60 minutes)
                  const startMinutes = hours * 60 + minutes;
                  const endMinutes = startMinutes + 60; // Full hour = 60 minutes
                  const endHours = Math.floor(endMinutes / 60);
                  const endMins = endMinutes % 60;

                  const actualEndTime = `${String(endHours).padStart(2, '0')}:${String(endMins).padStart(2, '0')}`;

                  return `${startTime} - ${actualEndTime} (${t('booking.fullLesson')})`;
                } else {
                  // For 25-minute lessons, show the original slot time
                  return selectedSlot.timeSlot;
                }
              })()}
            </Typography>
            <Typography variant="body1" gutterBottom>
              <strong>{t('booking.duration')}:</strong> {lessonDuration} {t('meetings.minutes')}
              {lessonDuration === '50' ? ` (${t('booking.fullLesson')})` : ` (${t('booking.halfLesson')})`}
            </Typography>
            <Typography variant="body1" gutterBottom>
              <strong>{t('booking.price')}:</strong> ${(() => {
                if (lessonDuration === '50') {
                  return teacher?.price_per_lesson;
                } else {
                  if (isTrialEligible) {
            return (teacher?.trialLessonPrice ?? teacher?.trial_lesson_price ?? teacher?.triallessonprice ?? (teacher?.price_per_lesson / 2)).toFixed ? (teacher?.trialLessonPrice ?? teacher?.trial_lesson_price ?? teacher?.triallessonprice ?? (teacher?.price_per_lesson / 2)).toFixed(2) : (teacher?.trialLessonPrice ?? teacher?.trial_lesson_price ?? teacher?.triallessonprice);
          }
          return (teacher?.price_per_lesson / 2).toFixed(2);
                }
              })()} {t('common.currency')}
            </Typography>

            <FormControl component="fieldset" sx={{ mt: 2, mb: 2 }}>
              <FormLabel component="legend">{t('booking.selectDuration')}</FormLabel>
              <RadioGroup
                name="lesson-duration"
                value={lessonDuration}
                onChange={(e) => {
                  setLessonDuration(e.target.value);
                }}
              >
                <FormControlLabel
                  value="25"
                  control={<Radio />}
                  label={`${t('booking.halfLesson')} (25 ${t('meetings.minutes')})`}
                />
                {selectedSlot && canBookFullHour(selectedSlot.day, selectedSlot.timeSlot) && (
                  <FormControlLabel
                    value="50"
                    control={<Radio />}
                    label={`${t('booking.fullLesson')} (50 ${t('meetings.minutes')}) - ${t('booking.consecutiveSlots')}`}
                  />
                )}
              </RadioGroup>
            </FormControl>

            {isTrialEligible && lessonDuration === '25' && (
              <Alert severity="info" sx={{ mt: 2 }}>
                {t('booking.trialLessonEligible', { price: teacher?.trialLessonPrice ?? teacher?.trial_lesson_price ?? teacher?.triallessonprice ?? '' })}
              </Alert>
            )}

          {bookingError && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {bookingError}
              </Alert>
            )}
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setConfirmDialogOpen(false)}>
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleConfirmBooking}
          variant="contained"
          color="primary"
          autoFocus
        >
          {t('booking.confirmAndPay')}
        </Button>
      </DialogActions>
    </Dialog>
  );

  // Main content
  const content = (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box>
          <Alert severity="error">{error}</Alert>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
            <Button
              variant="contained"
              startIcon={<ArrowBackIcon />}
              component={Link}
              to="/student/find-teacher"
            >
              {t('teacherDetails.backToSearch')}
            </Button>
          </Box>
        </Box>
      ) : (
        <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>
        <>
          {/* Back Button */}
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-start' }}>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              component={Link}
              to={`/student/teacher/${id}`}
            >
              {t('booking.backToTeacher')}
            </Button>
          </Box>

          {/* Current Time Display */}
          <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2, bgcolor: 'primary.main', color: 'white' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
              <Box>
                <Typography variant="h5" component="h1" sx={{ fontWeight: 'bold' }}>
                  {t('booking.bookLessonWith')} {teacher.full_name}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  {t('booking.pricePerLesson')}: ${teacher.price_per_lesson} {t('common.currency')}
                </Typography>
              </Box>
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  {studentProfile?.timezone ? (
                    moment(formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss'), 'YYYY-MM-DD HH:mm:ss').format('dddd, MMMM D, YYYY [at] h:mm A')
                  ) : (
                    format(currentTime, 'PPpp', {
                      locale: isRtl ? ar : enUS
                    })
                  )}
                </Typography>
                {studentProfile?.timezone && (
                  <Typography variant="caption" sx={{ opacity: 0.8 }}>
                    {studentProfile.timezone}
                  </Typography>
                )}
              </Box>
            </Box>
          </Paper>

          {/* Booking Instructions */}
          <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
              <InfoIcon color="primary" sx={{ mt: 0.5 }} />
              <Typography variant="h6" gutterBottom>
                {t('booking.instructions')}
              </Typography>
            </Box>
            <Typography variant="body1" paragraph>
              {t('booking.instructionsText')}
            </Typography>
          </Paper>

          {/* Week Navigation */}
          <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
            <Box sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: 2,
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
              color: 'white',
              p: 3,
              m: -3,
              mb: 3,
              borderRadius: '8px 8px 0 0'
            }}>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                  📅 {t('booking.weekNavigation')}
                </Typography>
                <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                  {t('booking.weekOf')} {format(currentWeekStart, 'MMM d', { locale: isRtl ? ar : enUS })} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy', { locale: isRtl ? ar : enUS })}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Tooltip title={t('booking.previousWeek')}>
                  <span>
                    <IconButton
                      onClick={goToPreviousWeek}
                      disabled={isPreviousWeekDisabled()}
                      sx={{
                        color: 'white',
                        bgcolor: 'rgba(255, 255, 255, 0.1)',
                        '&:hover': {
                          bgcolor: 'rgba(255, 255, 255, 0.2)',
                        },
                        '&:disabled': {
                          color: 'rgba(255, 255, 255, 0.3)',
                          bgcolor: 'rgba(255, 255, 255, 0.05)',
                        }
                      }}
                    >
                      <ChevronLeftIcon />
                    </IconButton>
                  </span>
                </Tooltip>
                <Tooltip title={t('booking.nextWeek')}>
                  <span>
                    <IconButton
                      onClick={goToNextWeek}
                      disabled={isNextWeekDisabled()}
                      sx={{
                        color: 'white',
                        bgcolor: 'rgba(255, 255, 255, 0.1)',
                        '&:hover': {
                          bgcolor: 'rgba(255, 255, 255, 0.2)',
                        },
                        '&:disabled': {
                          color: 'rgba(255, 255, 255, 0.3)',
                          bgcolor: 'rgba(255, 255, 255, 0.05)',
                        }
                      }}
                    >
                      <ChevronRightIcon />
                    </IconButton>
                  </span>
                </Tooltip>
              </Box>
            </Box>
          </Paper>

          {/* Weekly Calendar */}
          <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
            <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
              <CalendarMonthIcon color="primary" />
              {t('booking.selectTimeSlot')}
            </Typography>

            {teacher.available_hours && (() => {
              try {
                const parsedHours = typeof teacher.available_hours === 'string'
                  ? JSON.parse(teacher.available_hours)
                  : teacher.available_hours;
                return (
                  <BookableHoursTable
                    availableHours={parsedHours}
                    loading={loading}
                    showStats={true}
                    onSlotSelect={handleTableSlotSelect}
                    currentWeekStart={currentWeekStart}
                    daysOfWeek={daysOfWeek}
                    isSlotInPast={isSlotInPast}
                    studentTimezone={studentProfile?.timezone}
                  />
                );
              } catch (error) {
                console.error('Error parsing available hours:', error);
                return renderWeeklyCalendar();
              }
            })()}
          </Paper>

          {/* Confirmation Dialog */}
          {renderConfirmDialog()}
        </>
        </ProfileCompletionAlert>
      )}
    </Container>
  );

  return <Layout>{content}</Layout>;
};

export default BookingPage;
