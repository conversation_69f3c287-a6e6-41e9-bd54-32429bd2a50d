import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import axios from '../../utils/axios';
import { timezones } from '../../utils/constants';
import {
  Button,
  TextField,
  Grid,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  FormHelperText,
  InputAdornment,
  Paper,
  Avatar,

  Alert,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Divider,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CropImageDialog from '../common/CropImageDialog';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import VideoFileIcon from '@mui/icons-material/VideoFile';
import LinkIcon from '@mui/icons-material/Link';
import OndemandVideoIcon from '@mui/icons-material/OndemandVideo';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';

// Función para calcular lo que recibirá el profesor después de la comisión
const calculateTeacherEarnings = (price) => {
  // Convertir a número para asegurarnos
  const lessonPrice = Number(price);

  // Si el precio no es válido, devolver 0
  if (!lessonPrice || lessonPrice < 3) return 0;

  // Calcular la comisión según el precio
  let commissionRate;
  if (lessonPrice === 3) {
    commissionRate = 0.333; // 33.3%
  } else if (lessonPrice === 4) {
    commissionRate = 0.25; // 25%
  } else if (lessonPrice === 5) {
    commissionRate = 0.20; // 20%
  } else if (lessonPrice === 6) {
    commissionRate = 0.167; // 16.7%
  } else {
    commissionRate = 0.15; // 15% para $7 o más
  }

  // Calcular lo que recibe el profesor
  const teacherEarnings = lessonPrice * (1 - commissionRate);
  return teacherEarnings;
};

const ApplicationForm = ({ onSubmit, isEdit = false }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { currentUser, updateUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [fieldErrors, setFieldErrors] = useState({});
  const [success, setSuccess] = useState(false);
  const [categories, setCategories] = useState([]);
  const [languages, setLanguages] = useState([]);
  // Crop dialog state
  const [cropDialogOpen, setCropDialogOpen] = useState(false);
  const [tempImageSrc, setTempImageSrc] = useState('');

  const [formData, setFormData] = useState({
    country: '',
    residence: '',
    nativeLanguage: '',
    teachingLanguages: [],
    courseTypes: [],
    qualifications: '',
    teachingExperience: 0,
    introVideoUrl: '',
    cv: '',
    profilePicture: null,
    profilePicturePreview: '',
    commitmentAccepted: false,
    availableHours: {
      monday: [],
      tuesday: [],
      wednesday: [],
      thursday: [],
      friday: [],
      saturday: [],
      sunday: []
    },
    pricePerLesson: '',
    trialLessonPrice: '',
    timezone: '',

    phone: '',
  });

  // Estado para el diálogo de compromiso
  const [commitmentDialogOpen, setCommitmentDialogOpen] = useState(false);





  const timeSlots = [
    { key: "00:00-01:00", label: t('hours.hour1') },
    { key: "01:00-02:00", label: t('hours.hour2') },
    { key: "02:00-03:00", label: t('hours.hour3') },
    { key: "03:00-04:00", label: t('hours.hour4') },
    { key: "04:00-05:00", label: t('hours.hour5') },
    { key: "05:00-06:00", label: t('hours.hour6') },
    { key: "06:00-07:00", label: t('hours.hour7') },
    { key: "07:00-08:00", label: t('hours.hour8') },
    { key: "08:00-09:00", label: t('hours.hour9') },
    { key: "09:00-10:00", label: t('hours.hour10') },
    { key: "10:00-11:00", label: t('hours.hour11') },
    { key: "11:00-12:00", label: t('hours.hour12') },
    { key: "12:00-13:00", label: t('hours.hour13') },
    { key: "13:00-14:00", label: t('hours.hour14') },
    { key: "14:00-15:00", label: t('hours.hour15') },
    { key: "15:00-16:00", label: t('hours.hour16') },
    { key: "16:00-17:00", label: t('hours.hour17') },
    { key: "17:00-18:00", label: t('hours.hour18') },
    { key: "18:00-19:00", label: t('hours.hour19') },
    { key: "19:00-20:00", label: t('hours.hour20') },
    { key: "20:00-21:00", label: t('hours.hour21') },
    { key: "21:00-22:00", label: t('hours.hour22') },
    { key: "22:00-23:00", label: t('hours.hour23') },
    { key: "23:00-00:00", label: t('hours.hour24') }
  ];

  const days = {
    monday: t('days.monday'),
    tuesday: t('days.tuesday'),
    wednesday: t('days.wednesday'),
    thursday: t('days.thursday'),
    friday: t('days.friday'),
    saturday: t('days.saturday'),
    sunday: t('days.sunday')
  };

  // Load saved form data from localStorage
  useEffect(() => {
    const loadSavedFormData = () => {
      try {


        const savedData = localStorage.getItem('teacherApplicationForm');
        if (savedData) {
          const parsedData = JSON.parse(savedData);

          // Merge with default values to ensure all fields exist
          // Registrar los datos cargados
          console.log('Loading saved form data:', parsedData);

          setFormData(prevData => ({
            ...prevData,
            ...parsedData,
            // Keep these fields as they are
            profilePicture: prevData.profilePicture,
            commitmentDocument: prevData.commitmentDocument, // Mantener el objeto File
            profilePicturePreview: parsedData.profilePicturePreview || prevData.profilePicturePreview
          }));
        }

        // Load video URL for display
        const savedVideoUrl = localStorage.getItem('teacherVideoUrl');
        // Load relative video path for form submission
        const savedVideoRelativePath = localStorage.getItem('teacherVideoRelativePath');

        if (savedVideoUrl) {
          setFormData(prevData => ({
            ...prevData,
            // Use full URL for display
            introVideoUrl: savedVideoUrl,
            // Use relative path for form submission
            introVideoRelativePath: savedVideoRelativePath
          }));
        }
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    };

    loadSavedFormData();
  }, [currentUser]);

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    const saveFormData = () => {
      try {
        // Create a copy without file objects which can't be serialized
        const dataToSave = {
          ...formData,
          profilePicture: null,
          commitmentDocument: null // No podemos serializar objetos File
        };

        localStorage.setItem('teacherApplicationForm', JSON.stringify(dataToSave));
      } catch (error) {
        console.error('Error saving form data:', error);
      }
    };

    // Don't save if form is empty (initial load)
    if (formData.country || formData.residence || formData.teachingLanguages.length > 0) {
      saveFormData();
    }
  }, [formData]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [categoriesResponse, languagesResponse] = await Promise.all([
          axios.get('/api/teacher/categories'),
          axios.get('/api/teacher/languages')
        ]);

        setCategories(categoriesResponse.data);
        setLanguages(languagesResponse.data);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(t('teacher.errorFetchingData'));
      }
    };

    fetchData();
  }, [t]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Limit teachingExperience field to two numeric digits (0-99)
    if (name === 'teachingExperience') {
      let numeric = String(value).replace(/\D/g, ''); // keep digits only
      if (numeric.length > 2) {
        numeric = numeric.slice(0, 2);
      }
      setFormData(prev => ({ ...prev, [name]: numeric }));
      return;
    }

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleLanguageChange = (e) => {
    const { value } = e.target;
    setFormData(prev => ({
      ...prev,
      teachingLanguages: value
    }));
  };

  const handleProfilePictureChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setTempImageSrc(url);
      setCropDialogOpen(true);
    }
  };

  // Manejar la apertura del diálogo de compromiso
  const handleOpenCommitmentDialog = () => {
    setCommitmentDialogOpen(true);
  };

  // Manejar el cierre del diálogo de compromiso
  const handleCloseCommitmentDialog = () => {
    setCommitmentDialogOpen(false);
  };

  // Manejar la aceptación del compromiso
  const handleAcceptCommitment = () => {
    setFormData(prev => ({
      ...prev,
      commitmentAccepted: true
    }));

    // Guardar en localStorage
    const currentFormData = JSON.parse(localStorage.getItem('teacherApplicationForm') || '{}');
    localStorage.setItem('teacherApplicationForm', JSON.stringify({
      ...currentFormData,
      commitmentAccepted: true
    }));

    // Limpiar errores
    setFieldErrors(prev => ({
      ...prev,
      commitmentAccepted: null
    }));

    // Cerrar el diálogo
    setCommitmentDialogOpen(false);
  };

  // Manejar el rechazo del compromiso
  const handleRejectCommitment = () => {
    setFormData(prev => ({
      ...prev,
      commitmentAccepted: false
    }));

    // Guardar en localStorage
    const currentFormData = JSON.parse(localStorage.getItem('teacherApplicationForm') || '{}');
    localStorage.setItem('teacherApplicationForm', JSON.stringify({
      ...currentFormData,
      commitmentAccepted: false
    }));

    // Cerrar el diálogo
    setCommitmentDialogOpen(false);
  };

  // Video handling functions removed as we're using a separate page for video upload

  // Removed handleTimeSlotChange and handleSelectAllDay functions
  // These are now handled in the AvailableHours component

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setFieldErrors({});
    setLoading(true);

    // Validate form fields
    const errors = {};
    const requiredFields = [
      'country', 'residence', 'nativeLanguage', 'teachingLanguages', 'courseTypes',
      'qualifications', 'teachingExperience', 'cv', 'pricePerLesson', 'trialLessonPrice', 'timezone'
    ];

    // Check required fields
  // Custom validation for trial lesson price being less than regular price
  if (formData.trialLessonPrice && formData.pricePerLesson && Number(formData.trialLessonPrice) >= Number(formData.pricePerLesson)) {
    errors.trialLessonPrice = t('teacher.trialPriceLessThanRegular') || 'يجب أن يكون سعر الدرس التجريبي أقل من سعر الدرس العادي';
  }
    requiredFields.forEach(field => {
      if (!formData[field] ||
          (Array.isArray(formData[field]) && formData[field].length === 0) ||
          formData[field] === '') {
        errors[field] = t('teacher.required');
      }
    });

    // Check video requirement
    if (!formData.introVideoUrl) {
      errors.introVideoUrl = t('teacher.required');
    }

    // Check commitment acceptance
    if (!formData.commitmentAccepted) {
      errors.commitmentAccepted = t('teacher.commitmentRequired') || 'يجب الموافقة على التعهد';
    }

    // Check profile picture requirement
    if (!formData.profilePicture) {
      errors.profilePicture = t('teacher.profilePictureRequired') || 'الصورة الشخصية مطلوبة';
    }

    // If there are validation errors, stop submission
    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      setError(t('teacher.formHasErrors'));
      setLoading(false);
      return;
    }

    try {
      const data = new FormData();

      // Log form data before submission
      console.log('Form data before submission:', formData);



      // Check if this is an application update (from localStorage)
      const isApplicationUpdate = localStorage.getItem('isApplicationUpdate') === 'true';
      if (isApplicationUpdate) {
        console.log('This is an application update - setting updateApplication flag');
        data.append('updateApplication', 'true');
        // NO eliminar la bandera aquí, se eliminará después de enviar el formulario
      }

      Object.keys(formData).forEach(key => {
        if (key === 'profilePicture' && formData[key]) {
          data.append(key, formData[key]);
        } else if (key === 'commitmentAccepted') {
          // Agregar el estado de aceptación del compromiso
          data.append(key, formData[key]);
        } else if (key === 'introVideoUrl') {
          // Skip the full URL version - we'll use the relative path instead
        } else if (key === 'introVideoRelativePath') {
          // Use the relative path for the database
          data.append('introVideoUrl', formData[key]);
        } else if (!['profilePicturePreview'].includes(key)) {
          let value = formData[key];

          // Handle arrays and objects
          if (Array.isArray(value) || (typeof value === 'object' && value !== null && !(value instanceof File))) {
            value = JSON.stringify(value);
          }

          // Convert empty strings to null for optional fields
          if (value === '') {
            value = null;
          }

          // Only append if value is not null/undefined
          if (value !== undefined && value !== null) {
            data.append(key, value);
          }
        }
      });

      // Log FormData contents
      for (let pair of data.entries()) {
        console.log('FormData entry:', pair[0], pair[1]);
      }

      await onSubmit(data);
      setSuccess(true);

      // Clear localStorage after successful submission
      localStorage.removeItem('teacherApplicationForm');
      localStorage.removeItem('teacherVideoUrl');
      localStorage.removeItem('teacherVideoRelativePath');
      localStorage.removeItem('isApplicationUpdate');

      // Restaurar el rol original si existe
      const originalRole = localStorage.getItem('originalTeacherRole');
      if (originalRole && updateUser && currentUser) {
        console.log('Restoring original role:', originalRole);
        updateUser({
          ...currentUser,
          role: originalRole
        });
        localStorage.removeItem('originalTeacherRole');
      }

      // Navigate to appropriate page after successful submission
      setTimeout(() => {
        if (isEdit) {
          navigate('/teacher/profile');
        } else {
          // For new applications, don't navigate - let the parent component handle the state
          console.log('Application submitted successfully, staying on page');
        }
      }, 2000);
    } catch (err) {
      console.error('Form submission error:', err);
      setError(err.message || t('common.errorOccurred'));
      setSuccess(false);
    } finally {
      setLoading(false);
    }
  };

  if (loading && !categories.length) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper elevation={3} sx={{ p: 4 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {t('teacher.applicationSuccess')}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="h4" component="h1" gutterBottom>
              {isEdit ? t('teacher.editApplication.formTitle') || 'تعديل بيانات التقديم' : t('teacher.application.title')}
            </Typography>
          </Grid>

          {/* Country */}
          <Grid item xs={12} md={6}>
            <TextField
              required
              fullWidth
              name="country"
              label={t('teacher.country')}
              value={formData.country}
              onChange={handleChange}
              error={!!fieldErrors.country}
              helperText={fieldErrors.country || ''}
            />
          </Grid>

          {/* Region of Residence */}
          <Grid item xs={12} md={6}>
            <TextField
              required
              fullWidth
              name="residence"
              label={t('teacher.residence')}
              value={formData.residence}
              onChange={handleChange}
              error={!!fieldErrors.residence}
              helperText={fieldErrors.residence || ''}
            />
          </Grid>

          {/* Native Language */}
          <Grid item xs={12} md={6}>
            <TextField
              required
              fullWidth
              name="nativeLanguage"
              label={t('teacher.nativeLanguage')}
              value={formData.nativeLanguage}
              onChange={handleChange}
              error={!!fieldErrors.nativeLanguage}
              helperText={fieldErrors.nativeLanguage || ''}
            />
          </Grid>

          {/* Teaching Languages */}
          <Grid item xs={12}>
            <FormControl fullWidth required error={!!fieldErrors.teachingLanguages}>
              <InputLabel>{t('teacher.teachingLanguages')}</InputLabel>
              <Select
                multiple
                name="teachingLanguages"
                value={formData.teachingLanguages}
                onChange={handleLanguageChange}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip
                        key={value}
                        label={languages.find(lang => lang.id === value)?.name || value}
                      />
                    ))}
                  </Box>
                )}
              >
                {languages.map((lang) => (
                  <MenuItem key={lang.id} value={lang.id}>
                    {lang.name}
                  </MenuItem>
                ))}
              </Select>
              {fieldErrors.teachingLanguages && (
                <Typography variant="body2" color="error">
                  {fieldErrors.teachingLanguages}
                </Typography>
              )}
            </FormControl>
          </Grid>

          {/* Course Types */}
          <Grid item xs={12}>
            <FormControl fullWidth required error={!!fieldErrors.courseTypes}>
              <InputLabel>{t('teacher.courseTypes')}</InputLabel>
              <Select
                multiple
                name="courseTypes"
                value={formData.courseTypes}
                onChange={handleChange}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip
                        key={value}
                        label={categories.find(cat => cat.id === value)?.name || value}
                      />
                    ))}
                  </Box>
                )}
              >
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.id}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
              {fieldErrors.courseTypes && (
                <Typography variant="body2" color="error">
                  {fieldErrors.courseTypes}
                </Typography>
              )}
            </FormControl>
          </Grid>

          {/* Qualifications */}
          <Grid item xs={12}>
            <TextField
              required
              fullWidth
              multiline
              rows={4}
              name="qualifications"
              label={t('teacher.qualifications')}
              placeholder={t('teacher.qualificationsPlaceholder')}
              value={formData.qualifications}
              onChange={handleChange}
              error={!!fieldErrors.qualifications}
              helperText={fieldErrors.qualifications || ''}
            />
          </Grid>

          {/* Teaching Experience */}
          <Grid item xs={12} md={6}>
            <TextField
              required
              fullWidth
              type="number"
              name="teachingExperience"
              label={t('teacher.teachingExperience')}
              value={formData.teachingExperience}
              onChange={handleChange}
              error={!!fieldErrors.teachingExperience}
              helperText={fieldErrors.teachingExperience || ''}
              inputProps={{ min: 0, max: 99 }}
            />
          </Grid>

          {/* Introduction Video */}
          <Grid item xs={12}>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="h6">{t('teacher.introVideo')}</Typography>
              </Box>

              {formData.introVideoUrl ? (
                <Card sx={{ mb: 2 }}>
                  <CardMedia
                    component="video"
                    controls
                    src={formData.introVideoUrl}
                    sx={{ height: 300 }}
                  />
                  <CardContent>
                    <Typography variant="body2" color="text.secondary">
                      {t('teacher.videoReady')}
                    </Typography>
                  </CardContent>
                  <CardActions>
                    <Button
                      size="small"
                      color="error"
                      onClick={() => {
                        localStorage.removeItem('teacherVideoUrl');
                        localStorage.removeItem('teacherVideoRelativePath');
                        setFormData(prev => ({
                          ...prev,
                          introVideoUrl: '',
                          introVideoRelativePath: ''
                        }));
                      }}
                    >
                      {t('teacher.deleteVideo')}
                    </Button>
                  </CardActions>
                </Card>
              ) : (
                <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                    <Typography variant="body1" color={fieldErrors.introVideoUrl ? "error" : "textSecondary"}>
                      {t('teacher.videoRequired')}
                    </Typography>

                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<OndemandVideoIcon />}
                      onClick={() => navigate('/teacher/upload-video')}
                    >
                      {t('teacher.uploadVideoNow')}
                    </Button>

                    {fieldErrors.introVideoUrl && (
                      <Typography variant="body2" color="error">
                        {fieldErrors.introVideoUrl}
                      </Typography>
                    )}

                    <Box sx={{ mt: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1, border: 1, borderColor: 'divider', width: '100%' }}>
                      <Typography variant="body2" color="textSecondary" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                        {t('teacher.allowedFormats')}:
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {t('teacher.videoFormats')}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" sx={{ fontWeight: 'bold', mt: 1, mb: 0.5 }}>
                        {t('teacher.maxFileSize')}:
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {t('teacher.maxVideoSize')}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              )}
            </Box>
          </Grid>

          {/* CV */}
          <Grid item xs={12}>
            <TextField
              required
              fullWidth
              multiline
              rows={8}
              name="cv"
              label={t('teacher.cv')}
              placeholder={t('teacher.cvPlaceholder')}
              value={formData.cv}
              onChange={handleChange}
              error={!!fieldErrors.cv}
              helperText={fieldErrors.cv ? fieldErrors.cv : t('teacher.cvHelperText')}
              inputProps={{ maxLength: 2000 }}
            />
            <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
              {formData.cv.length}/2000 {t('teacher.characters')}
            </Typography>
          </Grid>

          {/* Teacher Commitment */}
          <Grid item xs={12}>
            <Paper elevation={1} sx={{ p: 3, mb: 2, border: '1px dashed', borderColor: fieldErrors.commitmentAccepted ? 'error.main' : 'primary.main' }}>
              <Typography variant="h6" gutterBottom>
                {t('teacher.commitment') || 'تعهد المعلم'}
              </Typography>

              <Typography variant="body2" paragraph>
                {t('teacher.commitmentDescription') || 'يجب قراءة التعهد والموافقة عليه للاستمرار في عملية التقديم.'}
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mb: 2 }}>
                <Button
                  variant="contained"
                  color={formData.commitmentAccepted ? "success" : "primary"}
                  onClick={handleOpenCommitmentDialog}
                  startIcon={formData.commitmentAccepted ? <CheckCircleIcon /> : null}
                  sx={{ mb: 1 }}
                >
                  {formData.commitmentAccepted
                    ? (t('teacher.commitmentAccepted') || 'تم الموافقة على التعهد')
                    : (t('teacher.readCommitment') || 'قراءة التعهد والموافقة عليه')}
                </Button>

                {/* Estado de aceptación del compromiso */}
                {formData.commitmentAccepted !== undefined && (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 1,
                      borderRadius: 1,
                      bgcolor: formData.commitmentAccepted ? 'success.light' : 'error.light',
                      color: 'white'
                    }}
                  >
                    {formData.commitmentAccepted ? (
                      <>
                        <CheckCircleIcon sx={{ mr: 1 }} />
                        <Typography variant="body2">
                          {t('teacher.commitmentStatus.accepted') || 'لقد وافقت على التعهد'}
                        </Typography>
                      </>
                    ) : (
                      <>
                        <CancelIcon sx={{ mr: 1 }} />
                        <Typography variant="body2">
                          {t('teacher.commitmentStatus.rejected') || 'لم توافق على التعهد بعد'}
                        </Typography>
                      </>
                    )}
                  </Box>
                )}
              </Box>

              {fieldErrors.commitmentAccepted && (
                <Typography variant="body2" color="error" sx={{ mt: 1 }}>
                  {fieldErrors.commitmentAccepted}
                </Typography>
              )}
            </Paper>
          </Grid>

          {/* Commitment Dialog */}
          <Dialog
            open={commitmentDialogOpen}
            onClose={handleCloseCommitmentDialog}
            scroll="paper"
            maxWidth="md"
            fullWidth
            dir="rtl"
          >
            <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white', textAlign: 'center', py: 2 }}>
              <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
                {t('teacher.commitmentTitle') || 'تعهّد المدرّسين في منصة "علّمني أون لاين بجميع اللغات"'}
              </Typography>
            </DialogTitle>
            <DialogContent dividers sx={{ px: 4, py: 3 }}>
              <DialogContentText component="div">
                <Typography paragraph sx={{ fontSize: '1.1rem', fontWeight: 'bold', mb: 3 }}>
                  {t('teacher.commitmentText.intro')}
                </Typography>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Typography variant="h6" color="primary" sx={{ minWidth: '24px' }}>1.</Typography>
                    <Typography paragraph sx={{ m: 0 }}>
                      {t('teacher.commitmentText.point1')}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Typography variant="h6" color="primary" sx={{ minWidth: '24px' }}>2.</Typography>
                    <Typography paragraph sx={{ m: 0 }}>
                      {t('teacher.commitmentText.point2')}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Typography variant="h6" color="primary" sx={{ minWidth: '24px' }}>3.</Typography>
                    <Typography paragraph sx={{ m: 0 }}>
                      {t('teacher.commitmentText.point3')}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Typography variant="h6" color="primary" sx={{ minWidth: '24px' }}>4.</Typography>
                    <Typography paragraph sx={{ m: 0 }}>
                      {t('teacher.commitmentText.point4')}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Typography variant="h6" color="primary" sx={{ minWidth: '24px' }}>5.</Typography>
                    <Typography paragraph sx={{ m: 0 }}>
                      {t('teacher.commitmentText.point5')}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Typography variant="h6" color="primary" sx={{ minWidth: '24px' }}>6.</Typography>
                    <Typography paragraph sx={{ m: 0 }}>
                      {t('teacher.commitmentText.point6')}
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
                  <Typography paragraph sx={{ m: 0, fontWeight: 'bold' }}>
                    {t('teacher.commitmentText.conclusion')}
                  </Typography>
                </Box>
              </DialogContentText>
            </DialogContent>
            <DialogActions sx={{ justifyContent: 'center', p: 3, gap: 3 }}>
              <Button
                onClick={handleRejectCommitment}
                color="error"
                variant="outlined"
                startIcon={<CancelIcon />}
                sx={{ minWidth: 150, py: 1 }}
                size="large"
              >
                {t('teacher.reject') || 'أرفض التعهد'}
              </Button>
              <Button
                onClick={handleAcceptCommitment}
                color="success"
                variant="contained"
                startIcon={<CheckCircleIcon />}
                sx={{ minWidth: 150, py: 1 }}
                size="large"
              >
                {t('teacher.accept') || 'أوافق على التعهد'}
              </Button>
            </DialogActions>
          </Dialog>

          {/* Profile Picture Upload */}
          <Grid item xs={12} md={6}>
            <input
              type="file"
              accept="image/*"
              style={{ display: 'none' }}
              id="profile-picture-upload"
              onChange={handleProfilePictureChange}
            />
            <FormControl fullWidth error={!!fieldErrors.profilePicture}>
              <InputLabel shrink required>{t('teacher.profilePicture')}</InputLabel>
              <Box sx={{ mt: 1 }}>
                <label htmlFor="profile-picture-upload">
                  <Button
                    variant="outlined"
                    component="span"
                    fullWidth
                    startIcon={<CloudUploadIcon />}
                  >
                    {formData.profilePicture ? formData.profilePicture.name : t('teacher.profilePicturePlaceholder')}
                  </Button>
                </label>
              </Box>
              {formData.profilePicturePreview && (
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                  <Avatar src={formData.profilePicturePreview} alt="Profile preview" sx={{ width: 120, height: 120 }} />
                </Box>
              )}
              <Typography variant="body2" color={fieldErrors.profilePicture ? "error" : "textSecondary"}>
                {fieldErrors.profilePicture || t('teacher.fileTypes.image')}
              </Typography>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ border: 1, borderColor: 'divider', borderRadius: 1, p: 2, mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  {t('teacher.availableHours')}
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    // Guardar los datos actuales del formulario antes de navegar
                    const dataToSave = {
                      ...formData,
                      profilePicture: null
                    };
                    localStorage.setItem('teacherApplicationForm', JSON.stringify(dataToSave));

                    // Redirigir a la página correcta según el rol del usuario
                    if (currentUser?.role === 'platform_teacher') {
                      navigate('/teacher/manage-hours');
                    } else {
                      navigate('/teacher/available-hours');
                    }
                  }}
                >
                  {t('teacher.manageAvailableHours')}
                </Button>
              </Box>
              <Typography variant="body2" color="text.secondary">
                {t('teacher.availableHoursDescription')}
              </Typography>

              {/* Show summary of selected hours */}
              <Box sx={{ mt: 2 }}>
                {Object.entries(formData.availableHours).map(([day, slots]) => (
                  slots.length > 0 && (
                    <Box key={day} sx={{ mb: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                        {days[day]}: <span style={{ fontWeight: 'normal' }}>{slots.length} {t('teacher.timeSlots')}</span>
                      </Typography>
                    </Box>
                  )
                ))}

                {Object.values(formData.availableHours).every(slots => slots.length === 0) && (
                  <Typography variant="body2" color="error">
                    {t('teacher.noAvailableHours')}
                  </Typography>
                )}
              </Box>
            </Box>
          </Grid>

          {/* Price per Lesson */}
          <Grid item xs={12} md={6}>
            <TextField
              required
              fullWidth
              type="number"
              name="pricePerLesson"
              label={t('teacher.pricePerLesson')}
              placeholder={t('teacher.pricePerLessonPlaceholder')}
              value={formData.pricePerLesson}
              onChange={handleChange}
              error={!!fieldErrors.pricePerLesson}
              helperText={fieldErrors.pricePerLesson || ''}
              InputProps={{
                startAdornment: <InputAdornment position="start">$</InputAdornment>,
              }}
              inputProps={{ min: 3, max: 100 }}
            />

            {/* Teacher's Earnings */}
            {formData.pricePerLesson > 0 && (
              <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', border: '1px dashed', borderColor: 'primary.main', borderRadius: 1 }}>
                <Typography variant="subtitle2" color="primary" gutterBottom>
                  {t('teacher.yourEarnings') || 'ما ستحصل عليه بعد خصم العمولة:'}
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                  ${calculateTeacherEarnings(formData.pricePerLesson).toFixed(2)}
                </Typography>
              </Box>
            )}
          </Grid>

          {/* Trial Lesson Price */}
          <Grid item xs={12} md={6}>
            <TextField
              required
              fullWidth
              type="number"
              name="trialLessonPrice"
              label={t('teacher.trialLessonPrice') || 'سعر الدرس التجريبي'}
              placeholder={t('teacher.trialLessonPricePlaceholder') || 'أدخل سعر الدرس التجريبي'}
              value={formData.trialLessonPrice}
              onChange={handleChange}
              error={!!fieldErrors.trialLessonPrice}
              helperText={fieldErrors.trialLessonPrice || ''}
              InputProps={{
                startAdornment: <InputAdornment position="start">$</InputAdornment>,
              }}
              inputProps={{ min: 1, max: 100 }}
            />
          </Grid>

          {/* Timezone */}
          <Grid item xs={12} md={6}>
            <FormControl fullWidth required error={!!fieldErrors.timezone}>
              <InputLabel>{t('teacher.timezone')}</InputLabel>
              <Select
                name="timezone"
                value={formData.timezone}
                onChange={handleChange}
              >
                {timezones.map((timezone) => (
                  <MenuItem key={timezone.value} value={timezone.value}>
                    {timezone.label}
                  </MenuItem>
                ))}
              </Select>
              {fieldErrors.timezone && (
                <Typography variant="body2" color="error">
                  {fieldErrors.timezone}
                </Typography>
              )}
            </FormControl>
          </Grid>


          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              name="phone"
              label={t('teacher.phone')}
              value={formData.phone}
              onChange={handleChange}
              error={!!fieldErrors.phone}
              helperText={fieldErrors.phone || t('teacher.phoneHelp')}
              placeholder={t('teacher.phoneOptional') || 'اختياري'}
            />
          </Grid>

          <Grid item xs={12}>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              size="large"
              fullWidth
              disabled={loading}
            >
              {loading ? (
                <CircularProgress size={24} />
              ) : isEdit ? (
                t('teacher.editApplication.submitButton') || 'حفظ التعديلات'
              ) : (
                t('teacher.application.submit')
              )}
            </Button>
          </Grid>
        </Grid>

        {/* Crop dialog for profile picture */}
        <CropImageDialog
          open={cropDialogOpen}
          imageSrc={tempImageSrc}
          onClose={() => setCropDialogOpen(false)}
          onSave={(blob) => {
            const file = new File([blob], 'profile.jpg', { type: blob.type });
            setFormData(prev => ({
              ...prev,
              profilePicture: file,
              profilePicturePreview: URL.createObjectURL(blob)
            }));
            setCropDialogOpen(false);
          }}
        />
      </form>
    </Paper>
  );
};

export default ApplicationForm;
