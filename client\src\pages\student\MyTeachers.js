import React, { useEffect, useState } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  Avatar,
  Button,
  Box,
  CircularProgress,
  Alert,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Paper,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';

const MyTeachers = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { token } = useAuth();

  const [teachers, setTeachers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTeachers = async () => {
      if (!token) return;
      try {
        const { data } = await axios.get('/bookings/student', {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (data.success && Array.isArray(data.data)) {

            // Aggregate teachers from bookings (exclude cancelled)

            const teachersMap = {};
            data.data.forEach((booking, idx) => {

              const teacherProfileId = booking.teacher_profile_id || booking.teacherProfileId || booking.teacher?.profile_id;
              const teacherUserId = booking.teacher_user_id || booking.teacherUserId || booking.teacher_id || booking.teacherId || booking.teacher?.user_id || booking.teacher?.user?.id || booking.teacher?.id || booking.teacher?.account_id;
              const teacherId = teacherUserId || teacherProfileId;
              const teacherName = booking.teacher_name || booking.teacherName || booking.teacher?.full_name || booking.teacher?.name;
              let teacherAvatar =
                booking.teacher_picture ||
                booking.teacherPicture ||
                booking.teacher?.avatar_url ||
                booking.teacher?.avatar ||
                booking.teacher?.profile_picture_url ||
                booking.profile_picture_url;

              // Normalize avatar URL to ensure it starts with '/' if it's a local uploads path
              if (teacherAvatar && !teacherAvatar.startsWith('http')) {
                if (teacherAvatar.startsWith('uploads/')) {
                  teacherAvatar = `/${teacherAvatar}`;
                } else if (!teacherAvatar.startsWith('/')) {
                  teacherAvatar = `/uploads/${teacherAvatar}`;
                }
              }

              if (!teacherId) {
                return;
              }
              // Only include bookings that are not cancelled
              if (booking.status && booking.status.toLowerCase() === 'cancelled') {
                return;
              }

              if (!teachersMap[teacherId]) {
                teachersMap[teacherId] = {
                  id: teacherId,
                  profile_id: teacherProfileId,
                  user_id: teacherUserId,
                  full_name: teacherName,
                  avatar_url: teacherAvatar,
                  profile_picture_url: teacherAvatar,
                  lessons_count: 1,
                };
              } else {
                teachersMap[teacherId].lessons_count += 1;
              }
            });
            // Convert to array and sort by lessons count (most lessons first)
            let aggregated = Object.values(teachersMap).sort((a, b) => b.lessons_count - a.lessons_count);

            // For teachers missing user_id (id same as profile_id), fetch mapping
            const missing = aggregated.filter(t => String(t.id) === String(t.profile_id))
                                       .map(t => t.profile_id);
            if (missing.length) {
              try {
                const detailsPromises = missing.map(pid => axios.get(`/api/teachers/profile/${pid}`, {
                  headers: { 'Authorization': `Bearer ${token}` }
                }));
                const responses = await Promise.allSettled(detailsPromises);
                responses.forEach((res, idx) => {
                  if (res.status === 'fulfilled' && res.value.data && res.value.data.success) {
                    const data = res.value.data.data || res.value.data.teacher;
                    const userId = data?.user_id || data?.id;
                    const pId = missing[idx];
                    aggregated = aggregated.map(t =>
                      String(t.profile_id) === String(pId) ? { ...t, user_id: userId } : t
                    );
                  }
                });
              } catch(err) {
                console.error('Error fetching teacher mapping', err);
              }
            }

            setTeachers(aggregated);
        } else {
          setError(data.message || t('common.errorFetching'));
        }
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error('Error fetching teachers:', err);
        setError(err.response?.data?.message || t('common.errorFetching'));
      } finally {
        setLoading(false);
      }
    };

    fetchTeachers();
  }, [token, t]);

  return (
    <Layout>
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
        {t('student.myTeachers','أساتذتي')}
      </Typography>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '40vh' }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : teachers.length === 0 ? (
        <Typography variant="body1">{t('student.noTeachersYet','لم تقم بحجز أي درس بعد')}</Typography>
      ) : (
        <Table component={Paper} sx={{ mt:2 }}>
            <TableHead>
              <TableRow>
                <TableCell></TableCell>
                <TableCell sx={{ fontWeight:'bold' }}>{t('common.name','الاسم')}</TableCell>
                <TableCell sx={{ fontWeight:'bold' }} align="center">{t('student.lessonsTaken','عدد الدروس')}</TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {teachers.map((teacher) => (
                <TableRow key={teacher.id} hover>
                  <TableCell>
                    <Avatar src={teacher.avatar_url || teacher.profile_picture_url || '/assets/images/default-avatar.png'} alt={teacher.full_name} />
                  </TableCell>
                  <TableCell>{teacher.full_name}</TableCell>
                  <TableCell align="center">{teacher.lessons_count}</TableCell>
                  <TableCell>
                    <Button variant="contained" size="small" onClick={() => navigate(`/student/book/${teacher.user_id || teacher.id}`)}>
                      {t('booking.bookAgain','احجز مرة أخرى')}
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
      )}
    </Container>
    </Layout>
  );
};

export default MyTeachers;
