import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  Paper,
  Box,
  Divider,
  useTheme,
  Fade,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,

} from '@mui/material';
import {
  Gavel as GavelIcon,
  Security as SecurityIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import Layout from '../components/Layout';

const PlatformPolicy = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const [isRTL, setIsRTL] = useState(i18n.language === 'ar');
  
  useEffect(() => {
    setIsRTL(i18n.language === 'ar');
    document.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';
  }, [i18n.language]);



  const sectionIcons = {
    studentPolicy: <PersonIcon />,
    teacherPolicy: <SchoolIcon />,
    generalNotes: <InfoIcon />,
    privacyPolicy: <SecurityIcon />,
    bookingPolicy: <ScheduleIcon />,
    cancellationPolicy: <GavelIcon />,
  };

  return (
    <Layout>
      <Box
        sx={{
          py: 4,
          minHeight: '100vh',
          background: theme.palette.background.default,
        }}
      >
        <Container maxWidth="lg">

          <Fade in timeout={1000}>
            <Paper
              elevation={3}
              sx={{
                p: { xs: 3, md: 5 },
                mb: 4,
                borderRadius: 2,
              }}
            >
              <Typography
                variant="h3"
                component="h1"
                gutterBottom
                align={isRTL ? 'right' : 'left'}
                sx={{
                  mb: 4,
                  fontWeight: 800,
                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                  color: theme.palette.primary.main,
                  position: 'relative',
                  display: 'inline-block',
                  '&:after': {
                    content: '""',
                    position: 'absolute',
                    bottom: -8,
                    left: isRTL ? 'auto' : 0,
                    right: isRTL ? 0 : 'auto',
                    width: '60%',
                    height: 4,
                    backgroundColor: theme.palette.secondary.main,
                    borderRadius: 2
                  }
                }}
              >
                {t('privacyPolicy.title')}
              </Typography>

              <Typography
                variant="body1"
                paragraph
                align={isRTL ? 'right' : 'left'}
                sx={{
                  mb: 4,
                  lineHeight: 1.8,
                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                }}
              >
                {t('privacyPolicy.intro')}
              </Typography>

              <Divider sx={{ mb: 4 }} />

              {/* Booking Policy */}
              <Box sx={{ mb: 5 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                  <Box
                    sx={{
                      mr: isRTL ? 0 : 2,
                      ml: isRTL ? 2 : 0,
                      color: theme.palette.primary.main
                    }}
                  >
                    <ScheduleIcon />
                  </Box>
                  <Typography
                    variant="h5"
                    component="h2"
                    sx={{
                      fontWeight: 700,
                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                    }}
                  >
                    {t('privacyPolicy.bookingPolicy.title')}
                  </Typography>
                </Box>

                {/* Student Policy */}
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                    <Box
                      sx={{
                        mr: isRTL ? 0 : 1,
                        ml: isRTL ? 1 : 0,
                        color: theme.palette.secondary.main
                      }}
                    >
                      <PersonIcon fontSize="small" />
                    </Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    >
                      {t('privacyPolicy.studentPolicy.title')}
                    </Typography>
                  </Box>

                  <Box sx={{ pl: isRTL ? 0 : 4, pr: isRTL ? 4 : 0 }}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                        mb: 1
                      }}
                    >
                      {t('privacyPolicy.studentPolicy.cancellation.title')}
                    </Typography>
                    <Typography
                      variant="body1"
                      paragraph
                      align={isRTL ? 'right' : 'left'}
                      sx={{
                        mb: 2,
                        lineHeight: 1.8,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    >
                      {t('privacyPolicy.studentPolicy.cancellation.description')}
                    </Typography>

                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                        mb: 1
                      }}
                    >
                      {t('privacyPolicy.studentPolicy.attendance.title')}
                    </Typography>
                    <Typography
                      variant="body1"
                      paragraph
                      align={isRTL ? 'right' : 'left'}
                      sx={{
                        mb: 2,
                        lineHeight: 1.8,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    >
                      {t('privacyPolicy.studentPolicy.attendance.description')}
                    </Typography>
                  </Box>
                </Box>

                {/* Teacher Policy */}
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                    <Box
                      sx={{
                        mr: isRTL ? 0 : 1,
                        ml: isRTL ? 1 : 0,
                        color: theme.palette.secondary.main
                      }}
                    >
                      <SchoolIcon fontSize="small" />
                    </Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    >
                      {t('privacyPolicy.teacherPolicy.title')}
                    </Typography>
                  </Box>

                  <Box sx={{ pl: isRTL ? 0 : 4, pr: isRTL ? 4 : 0 }}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                        mb: 1
                      }}
                    >
                      {t('privacyPolicy.teacherPolicy.availability.title')}
                    </Typography>
                    <Typography
                      variant="body1"
                      paragraph
                      align={isRTL ? 'right' : 'left'}
                      sx={{
                        mb: 2,
                        lineHeight: 1.8,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    >
                      {t('privacyPolicy.teacherPolicy.availability.description')}
                    </Typography>

                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                        mb: 1
                      }}
                    >
                      {t('privacyPolicy.teacherPolicy.attendance.title')}
                    </Typography>
                    <Typography
                      variant="body1"
                      paragraph
                      align={isRTL ? 'right' : 'left'}
                      sx={{
                        mb: 2,
                        lineHeight: 1.8,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    >
                      {t('privacyPolicy.teacherPolicy.attendance.description')}
                    </Typography>
                  </Box>
                </Box>

                {/* General Notes */}
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                    <Box
                      sx={{
                        mr: isRTL ? 0 : 1,
                        ml: isRTL ? 1 : 0,
                        color: theme.palette.secondary.main
                      }}
                    >
                      <InfoIcon fontSize="small" />
                    </Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 600,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    >
                      {t('privacyPolicy.generalNotes.title')}
                    </Typography>
                  </Box>

                  <Box sx={{ pl: isRTL ? 0 : 4, pr: isRTL ? 4 : 0 }}>
                    <Typography
                      variant="body1"
                      paragraph
                      align={isRTL ? 'right' : 'left'}
                      sx={{
                        mb: 1,
                        lineHeight: 1.8,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    >
                      {t('privacyPolicy.generalNotes.timeZone')}
                    </Typography>
                    <Typography
                      variant="body1"
                      paragraph
                      align={isRTL ? 'right' : 'left'}
                      sx={{
                        mb: 2,
                        lineHeight: 1.8,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    >
                      {t('privacyPolicy.generalNotes.policyChanges')}
                    </Typography>
                  </Box>
                </Box>
              </Box>

              {/* Commission Policy */}
              <Box sx={{ mb: 5 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                  <Box
                    sx={{
                      mr: isRTL ? 0 : 2,
                      ml: isRTL ? 2 : 0,
                      color: theme.palette.primary.main
                    }}
                  >
                    <GavelIcon />
                  </Box>
                  <Typography
                    variant="h5"
                    component="h2"
                    sx={{
                      fontWeight: 700,
                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                    }}
                  >
                    {t('privacyPolicy.commissionPolicy.title')}
                  </Typography>
                </Box>

                <Typography
                  variant="body1"
                  paragraph
                  align={isRTL ? 'right' : 'left'}
                  sx={{
                    mb: 2,
                    lineHeight: 1.8,
                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                    pl: isRTL ? 0 : 4,
                    pr: isRTL ? 4 : 0,
                  }}
                >
                  {t('privacyPolicy.commissionPolicy.description')}
                </Typography>

                <List sx={{ pl: isRTL ? 0 : 4, pr: isRTL ? 4 : 0, mb: 2 }}>
                  <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
                    <ListItemText
                      primary={t('privacyPolicy.commissionPolicy.rates.3')}
                      primaryTypographyProps={{
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    />
                  </ListItem>
                  <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
                    <ListItemText
                      primary={t('privacyPolicy.commissionPolicy.rates.4')}
                      primaryTypographyProps={{
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    />
                  </ListItem>
                  <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
                    <ListItemText
                      primary={t('privacyPolicy.commissionPolicy.rates.5')}
                      primaryTypographyProps={{
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    />
                  </ListItem>
                  <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
                    <ListItemText
                      primary={t('privacyPolicy.commissionPolicy.rates.6')}
                      primaryTypographyProps={{
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    />
                  </ListItem>
                  <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
                    <ListItemText
                      primary={t('privacyPolicy.commissionPolicy.rates.7')}
                      primaryTypographyProps={{
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      }}
                    />
                  </ListItem>
                </List>

                <Typography
                  variant="body1"
                  paragraph
                  align={isRTL ? 'right' : 'left'}
                  sx={{
                    mt: 3,
                    mb: 2,
                    lineHeight: 1.8,
                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                    pl: isRTL ? 0 : 4,
                    pr: isRTL ? 4 : 0,
                    fontStyle: 'italic',
                    color: theme.palette.text.secondary,
                    backgroundColor: theme.palette.action.hover,
                    padding: 2,
                    borderRadius: 1,
                    border: `1px solid ${theme.palette.divider}`,
                  }}
                >
                  {t('privacyPolicy.commissionPolicy.explanation')}
                </Typography>
              </Box>

              {/* Teacher Commitment */}
              <Box sx={{ mb: 5 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                  <Box
                    sx={{
                      mr: isRTL ? 0 : 2,
                      ml: isRTL ? 2 : 0,
                      color: theme.palette.primary.main
                    }}
                  >
                    <SchoolIcon />
                  </Box>
                  <Typography
                    variant="h5"
                    component="h2"
                    sx={{
                      fontWeight: 700,
                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                    }}
                  >
                    {t('teacher.commitmentTitle')}
                  </Typography>
                </Box>

                <Typography
                  variant="body1"
                  paragraph
                  align={isRTL ? 'right' : 'left'}
                  sx={{
                    mb: 3,
                    lineHeight: 1.8,
                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                    pl: isRTL ? 0 : 4,
                    pr: isRTL ? 4 : 0,
                    fontSize: '1.1rem',
                    fontWeight: 'bold',
                  }}
                >
                  {t('teacher.commitmentText.intro')}
                </Typography>

                <Box sx={{ pl: isRTL ? 0 : 4, pr: isRTL ? 4 : 0, mb: 2 }}>
                  <Box sx={{ display: 'flex', gap: 2, mb: 2, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                    <Typography variant="h6" color="primary" sx={{ minWidth: '24px', textAlign: isRTL ? 'right' : 'left' }}>1.</Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        lineHeight: 1.8,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                        textAlign: isRTL ? 'right' : 'left'
                      }}
                    >
                      {t('teacher.commitmentText.point1')}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2, mb: 2, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                    <Typography variant="h6" color="primary" sx={{ minWidth: '24px', textAlign: isRTL ? 'right' : 'left' }}>2.</Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        lineHeight: 1.8,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                        textAlign: isRTL ? 'right' : 'left'
                      }}
                    >
                      {t('teacher.commitmentText.point2')}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2, mb: 2, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                    <Typography variant="h6" color="primary" sx={{ minWidth: '24px', textAlign: isRTL ? 'right' : 'left' }}>3.</Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        lineHeight: 1.8,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                        textAlign: isRTL ? 'right' : 'left'
                      }}
                    >
                      {t('teacher.commitmentText.point3')}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2, mb: 2, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                    <Typography variant="h6" color="primary" sx={{ minWidth: '24px', textAlign: isRTL ? 'right' : 'left' }}>4.</Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        lineHeight: 1.8,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                        textAlign: isRTL ? 'right' : 'left'
                      }}
                    >
                      {t('teacher.commitmentText.point4')}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2, mb: 2, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                    <Typography variant="h6" color="primary" sx={{ minWidth: '24px', textAlign: isRTL ? 'right' : 'left' }}>5.</Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        lineHeight: 1.8,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                        textAlign: isRTL ? 'right' : 'left'
                      }}
                    >
                      {t('teacher.commitmentText.point5')}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2, mb: 3, flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                    <Typography variant="h6" color="primary" sx={{ minWidth: '24px', textAlign: isRTL ? 'right' : 'left' }}>6.</Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        lineHeight: 1.8,
                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                        textAlign: isRTL ? 'right' : 'left'
                      }}
                    >
                      {t('teacher.commitmentText.point6')}
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider', pl: isRTL ? 0 : 4, pr: isRTL ? 4 : 0 }}>
                  <Typography
                    variant="body1"
                    sx={{
                      fontWeight: 'bold',
                      lineHeight: 1.8,
                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                      textAlign: isRTL ? 'right' : 'left'
                    }}
                  >
                    {t('teacher.commitmentText.conclusion')}
                  </Typography>
                </Box>
              </Box>

            </Paper>
          </Fade>
        </Container>
      </Box>
    </Layout>
  );
};

export default PlatformPolicy;
