import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  Box,
  Fade,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  useTheme,
  alpha,
  Chip,
  Alert,
  Grid,
} from '@mui/material';
import {
  Email as EmailIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Payment as PaymentIcon,
  CurrencyExchange as CurrencyExchangeIcon,
  Receipt as ReceiptIcon,
  Warning as WarningIcon,
  Security as SecurityIcon,
  AccessTime as AccessTimeIcon,
  CreditCard as CreditCardIcon,
} from '@mui/icons-material';

const BookingPaymentPolicy = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';
  const policy = t('policies.bookingPayment', { returnObjects: true });

  const SectionCard = ({ title, children, bg }) => (
    <Card
      elevation={2}
      sx={{
        mb: 4,
        borderRadius: 3,
        ...(isRtl
        ? { borderRight: `6px solid ${theme.palette.primary.main}` }
        : { borderLeft: `6px solid ${theme.palette.primary.main}` }),
        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),
      }}
    >
      <CardContent>
        <Typography
          variant="h5"
          fontWeight={600}
          color={theme.palette.primary.main}
          mb={2}
          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
        >
          {title}
        </Typography>
        {children}
      </CardContent>
    </Card>
  );

  const renderSection = (section) => (
    <SectionCard title={section.title}>
      {section.points && (
        <List>
          {section.points.map((item, idx) => (
            <ListItem key={idx} sx={{
              pl: isRtl ? 0 : 2,
              pr: isRtl ? 2 : 0,
              flexDirection: 'row',
              textAlign: isRtl ? 'right' : 'left',
            }}>
              <ListItemIcon sx={{
                minWidth: 'auto',
                mx: isRtl ? 0 : 1,
                ml: isRtl ? 1 : 0,
              }}>
                <CheckCircleIcon color="success" />
              </ListItemIcon>
              <ListItemText primary={item} />
            </ListItem>
          ))}
        </List>
      )}
      {section.description && <Typography>{section.description}</Typography>}
    </SectionCard>
  );




  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,
        pt: 4,
        pb: 8,
      }}
    >
      <Container maxWidth="lg">
        <Fade in timeout={800}>
          <Box sx={{ p: { xs: 2, md: 4 }, direction: isRtl ? 'rtl' : 'ltr' }}>
            <Typography variant="h4" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
              {policy.title}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary" mb={3}>
              {policy.subtitle}
            </Typography>
            <Divider sx={{ mb: 3 }} />
            <Typography variant="body2" color="text.secondary" mb={2}>{policy.effectiveDate}</Typography>
            {renderSection(policy.section1)}
            {renderSection(policy.section2)}
            {renderSection(policy.section3)}
            {renderSection(policy.section4)}
            {renderSection(policy.section5)}
            {renderSection(policy.section6)}
            {renderSection(policy.section7)}
            {renderSection(policy.section8)}
            {/* Features */}
            <Divider sx={{ my: 3 }} />
            <Grid container spacing={2} mb={3}>
              <Grid item xs={12} md={6}>
                <Alert icon={<SecurityIcon />} severity="success" sx={{ direction: isRtl ? 'rtl' : 'ltr' }}>
                  <Typography fontWeight={600}>{policy.features.securePayments}</Typography>
                  <Typography>{policy.features.securePaymentsDesc}</Typography>
                </Alert>
              </Grid>
              <Grid item xs={12} md={6}>
                <Alert icon={<CurrencyExchangeIcon />} severity="info" sx={{ direction: isRtl ? 'rtl' : 'ltr' }}>
                  <Typography fontWeight={600}>{policy.features.multipleCurrencies}</Typography>
                  <Typography>{policy.features.multipleCurrenciesDesc}</Typography>
                </Alert>
              </Grid>
              <Grid item xs={12} md={6}>
                <Alert icon={<ReceiptIcon />} severity="info" sx={{ direction: isRtl ? 'rtl' : 'ltr' }}>
                  <Typography fontWeight={600}>{policy.features.instantReceipts}</Typography>
                  <Typography>{policy.features.instantReceiptsDesc}</Typography>
                </Alert>
              </Grid>
              <Grid item xs={12} md={6}>
                <Alert icon={<CheckCircleIcon />} severity="success" sx={{ direction: isRtl ? 'rtl' : 'ltr' }}>
                  <Typography fontWeight={600}>{policy.features.instantConfirmation}</Typography>
                  <Typography>{policy.features.instantConfirmationDesc}</Typography>
                </Alert>
              </Grid>
            </Grid>
            {/* Contact */}
            <Box>
              <Typography variant="h6" fontWeight={600} mb={1}>{policy.section8.title || policy.section8.description}</Typography>
              <Typography mb={1}>{policy.section8.description}</Typography>
              <Alert icon={<EmailIcon />} severity="info" sx={{ direction: isRtl ? 'rtl' : 'ltr' }}>{policy.section8.email}</Alert>
            </Box>
          </Box>
        </Fade>
      </Container>
    </Box>
  );
};

export default BookingPaymentPolicy;