{"title": "Meeting Issues Management", "columns": {"id": "ID", "issue_type": "Issue Type", "status": "Status", "user": "User", "user_email": "Email", "issue_source": "Source", "meeting_name": "Meeting Name", "meeting_date": "Meeting Date", "meeting_time": "Meeting Time", "booking_datetime": "Booking Date", "booking_status": "Booking Status", "room_name": "Room Name", "description": "Description", "created_at": "Created At", "updated_at": "Updated At", "actions": "Actions"}, "type": {"teacher_absent": "Teacher Absent", "technical_issue": "Technical Issue", "pending": "Pending", "no_issue": "No Issue"}, "status": {"pending": "Pending", "resolved": "Resolved"}, "details": {"title": "Issue Details", "basic_info": "Basic Information", "user_info": "User Information", "meeting_info": "Meeting Information", "booking_info": "Booking Information", "timestamps": "Timestamps", "no_description": "No description available", "view": "View Details", "close": "Close"}, "actions": {"resolve": "Resolve Issue", "resolving": "Resolving..."}, "success": {"issue_resolved": "Issue resolved successfully"}, "errors": {"fetch_failed": "Failed to fetch data", "resolve_failed": "Failed to resolve issue"}}