import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  Alert,
  Divider,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  CircularProgress
} from '@mui/material';
import {
  Language as LanguageIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Lock as LockIcon,
  Edit as EditIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout';
import ReactPlayer from 'react-player';
import { timezones } from '../../utils/constants';

// Function to calculate teacher earnings after commission
const calculateTeacherEarnings = (price) => {
  const lessonPrice = Number(price);
  if (!lessonPrice || lessonPrice < 3) return 0;

  let commissionRate;
  if (lessonPrice === 3) {
    commissionRate = 0.333; // 33.3%
  } else if (lessonPrice === 4) {
    commissionRate = 0.25; // 25%
  } else if (lessonPrice === 5) {
    commissionRate = 0.20; // 20%
  } else if (lessonPrice === 6) {
    commissionRate = 0.167; // 16.7%
  } else {
    commissionRate = 0.15; // 15% for $7+
  }

  const teacherEarnings = lessonPrice * (1 - commissionRate);
  return teacherEarnings;
};

// Function to extract YouTube video ID from URL
const getYoutubeVideoId = (url) => {
  if (!url) return null;

  // Regular expressions to match different YouTube URL formats
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
  const match = url.match(regExp);

  return (match && match[2].length === 11) ? match[2] : null;
};

// Function to check if a URL is a local video file
const isLocalVideoFile = (url) => {
  if (!url) return false;
  return url.startsWith('/uploads/videos/');
};

// Function to check if a URL is a YouTube video
const isYoutubeVideo = (url) => {
  if (!url) return false;
  return getYoutubeVideoId(url) !== null;
};

const Profile = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { user, updateUser } = useAuth();
  const [isRtl, setIsRtl] = useState(i18n.language === 'ar');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPassword, setShowPassword] = useState({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false
  });
  const [openPasswordDialog, setOpenPasswordDialog] = useState(false);
  const [openEditApplicationDialog, setOpenEditApplicationDialog] = useState(false);
  const [openEditTeachingInfoDialog, setOpenEditTeachingInfoDialog] = useState(false);
  const [languages, setLanguages] = useState([]);
  const [categories, setCategories] = useState([]);
  const [applicationStatus, setApplicationStatus] = useState('');
  const [profileUpdateStatus, setProfileUpdateStatus] = useState(null);

  // Separate state for user info and teacher profile
  const [userData, setUserData] = useState({
    fullName: user?.full_name || '',
    email: user?.email || '',
    gender: user?.gender || '',
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [profileData, setProfileData] = useState({
    phone: '',
    country: '',
    residence: '',
    nativeLanguage: '',
    teachingLanguages: [],
    courseTypes: [],
    qualifications: '',
    teachingExperience: '',
    availableHours: {},
    pricePerLesson: '',
    trialLessonPrice: '',
    timezone: '',
    profilePictureUrl: '',
    cv: '',
    introVideoUrl: '',
    commitmentAccepted: false,
  });



  const [editTeachingData, setEditTeachingData] = useState({
    phone: '',
    nativeLanguage: '',
    teachingLanguages: [],
    courseTypes: [],
    pricePerLesson: '',
    trialLessonPrice: '',
    timezone: '',
  });

  const [fieldErrors, setFieldErrors] = useState({});

  // Auto-hide messages after 3 seconds
  useEffect(() => {
    let timer;
    if (success || error) {
      timer = setTimeout(() => {
        setSuccess('');
        setError('');
      }, 3000);
    }
    return () => clearTimeout(timer);
  }, [success, error]);

  // دالة جلب حالة طلب التعديل
  const fetchProfileUpdateStatus = async () => {
    try {
      const response = await axios.get('/api/teacher/profile-update-status');
      if (response.data.success && response.data.hasUpdate) {
        setProfileUpdateStatus(response.data.update);
      } else {
        setProfileUpdateStatus(null);
      }
    } catch (error) {
      console.error('Error fetching profile update status:', error);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch teacher profile
        const profileResponse = await axios.get('/teacher/profile');
        if (profileResponse.data.success) {
          const { user, profile } = profileResponse.data;
          setUserData(prev => ({
            ...prev,
            fullName: user?.full_name || '',
            email: user?.email || '',
            gender: user?.gender || '',
          }));

          // Set application status
          setApplicationStatus(profile?.status || '');

          // Fetch languages
          const languagesResponse = await axios.get('/teacher/languages');
          const languagesMap = {};
          languagesResponse.data.forEach(lang => {
            languagesMap[lang.id] = lang.name;
          });
          setLanguages(languagesResponse.data); // Store as array for dialog

          // Fetch categories
          const categoriesResponse = await axios.get('/teacher/categories');
          setCategories(categoriesResponse.data);

          // Ensure teaching_languages is an array and map IDs to names
          const teachingLangs = Array.isArray(profile.teaching_languages)
            ? profile.teaching_languages.map(id => {
                const lang = languagesResponse.data.find(l => l.id === id);
                return lang ? lang.name : id;
              })
            : [];

          // Ensure course_types is an array and map IDs to names
          const courseTypeNames = Array.isArray(profile.course_types)
            ? profile.course_types.map(id => {
                const category = categoriesResponse.data.find(c => c.id === id);
                return category ? category.name : id;
              })
            : [];

          // Update profile data with language names instead of IDs
          setProfileData({
            phone: profile?.phone || '',
            country: profile?.country || '',
            residence: profile?.residence || '',
            nativeLanguage: profile?.native_language || '',
            teachingLanguages: teachingLangs, // استخدام الأسماء المحولة بدلاً من IDs
            courseTypes: courseTypeNames, // استخدام الأسماء المحولة بدلاً من IDs
            qualifications: profile?.qualifications || '',
            teachingExperience: profile?.teaching_experience || '',
            availableHours: profile?.available_hours || {},
            pricePerLesson: profile?.price_per_lesson || '',
            trialLessonPrice: profile?.trial_lesson_price || '',
            timezone: profile?.timezone || '',

            profilePictureUrl: profile?.profile_picture_url || '',
            cv: profile?.cv || '',
            introVideoUrl: profile?.intro_video_url || '',
            commitmentAccepted: profile?.commitment_accepted || false,
          });

          console.log('Profile data loaded:', {
            ...profile,
            commitment_accepted: profile?.commitment_accepted,
            commitmentAccepted: profile?.commitment_accepted || false
          });
          console.log('Setting profileData.commitmentAccepted to:', profile?.commitment_accepted || false);
        }

        // جلب حالة طلب التعديل
        await fetchProfileUpdateStatus();
      } catch (error) {
        console.error('Error fetching data:', error);
        setError(t('profile.errors.fetch'));
      }
    };

    fetchData();
  }, [t]);



  const handlePasswordChange = (prop) => (event) => {
    setPasswordData({ ...passwordData, [prop]: event.target.value });
  };

  const handleClickShowPassword = (field) => {
    setShowPassword({ ...showPassword, [field]: !showPassword[field] });
  };

  const handleOpenPasswordDialog = () => setOpenPasswordDialog(true);
  const handleClosePasswordDialog = () => {
    setOpenPasswordDialog(false);
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
    setShowPassword({
      currentPassword: false,
      newPassword: false,
      confirmPassword: false
    });
  };

  const handleUpdatePassword = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await axios.put('/auth/change-password', {
        current_password: passwordData.currentPassword,
        new_password: passwordData.newPassword
      });
      console.log('Password update response:', response.data);

      if (response.data.success) {
        const successMsg = t('profile.passwordUpdateSuccess');
        handleClosePasswordDialog();
        setSuccess(successMsg);
        setError('');
      }
    } catch (error) {
      console.error('Password update error:', error);
      setError(error.response?.data?.message || t('profile.errors.updateFailed'));
    } finally {
      setLoading(false);
    }
  };





  // Teaching info edit handlers
  const handleOpenEditTeachingInfoDialog = () => {
    // Convert language names back to IDs for editing
    const teachingLanguageIds = profileData.teachingLanguages.map(langName => {
      const langEntry = languages.find(lang => lang.name === langName);
      return langEntry ? langEntry.id : langName;
    });

    // Convert category names back to IDs for editing
    const courseTypeIds = profileData.courseTypes.map(catName => {
      const catEntry = categories.find(cat => cat.name === catName);
      return catEntry ? catEntry.id : catName;
    });

    setEditTeachingData({
      phone: profileData.phone,
      nativeLanguage: profileData.nativeLanguage,
      teachingLanguages: teachingLanguageIds,
      courseTypes: courseTypeIds,
      pricePerLesson: profileData.pricePerLesson,
      trialLessonPrice: profileData.trialLessonPrice,
      timezone: profileData.timezone,
    });
    setFieldErrors({});
    setOpenEditTeachingInfoDialog(true);
  };

  const handleCloseEditTeachingInfoDialog = () => {
    setOpenEditTeachingInfoDialog(false);
    setEditTeachingData({
      phone: profileData.phone,
      nativeLanguage: profileData.nativeLanguage,
      teachingLanguages: profileData.teachingLanguages,
      courseTypes: profileData.courseTypes,
      pricePerLesson: profileData.pricePerLesson,
      trialLessonPrice: profileData.trialLessonPrice,
      timezone: profileData.timezone,
    });
  };

  const handleEditTeachingChange = (prop) => (event) => {
    setEditTeachingData({ ...editTeachingData, [prop]: event.target.value });
  };

  const handleEditTeachingSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');
    setFieldErrors({});

    // Validate form fields
    const errors = {};
    const requiredFields = [
      'nativeLanguage', 'teachingLanguages', 'courseTypes',
      'pricePerLesson', 'trialLessonPrice', 'timezone'
    ];

    // Check required fields (phone is optional)
    requiredFields.forEach(field => {
      if (!editTeachingData[field] ||
          (Array.isArray(editTeachingData[field]) && editTeachingData[field].length === 0) ||
          editTeachingData[field] === '') {
        errors[field] = t('teacher.required');
      }
    });

    // Validate price range
    if (editTeachingData.pricePerLesson && (editTeachingData.pricePerLesson < 3 || editTeachingData.pricePerLesson > 100)) {
      errors.pricePerLesson = t('teacher.priceRange') || 'السعر يجب أن يكون بين $3 و $100';
    }

    // Validate trial lesson price
    if (!editTeachingData.trialLessonPrice) {
      errors.trialLessonPrice = t('teacher.required');
    } else if (Number(editTeachingData.trialLessonPrice) >= Number(editTeachingData.pricePerLesson)) {
      errors.trialLessonPrice = t('teacher.trialPriceLessThanRegular');
    }

    // If there are validation errors, stop submission
    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      setError(t('teacher.formHasErrors') || 'يرجى تصحيح الأخطاء في النموذج');
      setLoading(false);
      return;
    }

    try {
      const updateData = {
        phone: editTeachingData.phone,
        native_language: editTeachingData.nativeLanguage,
        teaching_languages: editTeachingData.teachingLanguages,
        course_types: editTeachingData.courseTypes,
        price_per_lesson: editTeachingData.pricePerLesson,
         trial_lesson_price: editTeachingData.trialLessonPrice,
        timezone: editTeachingData.timezone,
      };

      const response = await axios.put('/teacher/update-teaching-info', updateData);
      console.log('Teaching info update response:', response.data);

      if (response.data.success) {
        // Convert IDs back to names for display
        const teachingLanguageNames = editTeachingData.teachingLanguages.map(langId => {
          const langEntry = languages.find(lang => lang.id === langId);
          return langEntry ? langEntry.name : langId;
        });

        const courseTypeNames = editTeachingData.courseTypes.map(catId => {
          const catEntry = categories.find(cat => cat.id === catId);
          return catEntry ? catEntry.name : catId;
        });

        // Update local state
        setProfileData(prev => ({
          ...prev,
          phone: editTeachingData.phone,
          nativeLanguage: editTeachingData.nativeLanguage,
          teachingLanguages: teachingLanguageNames,
          courseTypes: courseTypeNames,
          pricePerLesson: editTeachingData.pricePerLesson,
           trialLessonPrice: editTeachingData.trialLessonPrice,
          timezone: editTeachingData.timezone,
        }));

        const successMsg = t('profile.teachingInfoUpdateSuccess');
        handleCloseEditTeachingInfoDialog();
        setSuccess(successMsg);
        setError('');
      }
    } catch (error) {
      console.error('Teaching info update error:', error);
      setError(error.response?.data?.message || t('profile.errors.updateFailed'));
    } finally {
      setLoading(false);
    }
  };

  // Función para manejar la edición de la aplicación del profesor
  const handleEditApplication = () => {
    setOpenEditApplicationDialog(true);
  };

  // Función para redirigir al profesor a la página de edición de aplicación
  const prepareApplicationEdit = () => {
    try {
      // Cerrar el diálogo
      setOpenEditApplicationDialog(false);

      // Redirigir a la página de edición de aplicación
      navigate('/teacher/edit-application');
    } catch (error) {
      console.error('Error preparing application edit:', error);
      setError(t('profile.errors.applicationPreparation'));
    }
  };

  // تحديث حالة طلب التعديل عند العودة من صفحة التعديل
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        fetchProfileUpdateStatus();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleVisibilityChange);
    };
  }, []);

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Paper elevation={3} sx={{ p: 4 }}>
          {/* Profile Picture Section */}
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 4 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              {t('profile.title')}
            </Typography>

            <Box sx={{ position: 'relative', mb: 3 }}>
              <Avatar
                src={profileData.profilePictureUrl ? (
                  profileData.profilePictureUrl.startsWith('http')
                    ? profileData.profilePictureUrl
                    : `https://allemnionline.com${profileData.profilePictureUrl}`
                ) : ''}
                alt={userData.fullName}
                sx={{
                  width: 120,
                  height: 120,
                  border: '2px solid',
                  borderColor: 'primary.main',
                  bgcolor: 'primary.main',
                  fontSize: '3rem'
                }}
              >
                {!profileData.profilePictureUrl && userData.fullName?.charAt(0)}
              </Avatar>
            </Box>

            <Typography variant="h6" gutterBottom>
              {userData.fullName}
            </Typography>
            <Typography variant="body1" color="textSecondary" sx={{ mb: 3 }}>
              {t(`role.${user?.role || 'teacher'}`)}
            </Typography>

            <Box sx={{ width: '100%', maxWidth: '500px', display: 'flex', flexDirection: 'column', gap: 2 }}>
              {error && (
                <Alert
                  severity="error"
                  variant="filled"
                  sx={{
                    width: '100%',
                    '& .MuiAlert-message': {
                      width: '100%',
                      textAlign: 'center'
                    }
                  }}
                >
                  {error}
                </Alert>
              )}

              {success && (
                <Alert
                  severity="success"
                  variant="filled"
                  sx={{
                    width: '100%',
                    '& .MuiAlert-message': {
                      width: '100%',
                      textAlign: 'center'
                    }
                  }}
                >
                  {success}
                </Alert>
              )}
            </Box>
          </Box>

          <Paper elevation={3} sx={{ p: 4, mb: 4, borderRadius: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h5" gutterBottom sx={{ mb: 0 }}>
                {t('profile.basicInfo')}
              </Typography>
              <Box>
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<EditIcon />}
                  onClick={handleOpenEditTeachingInfoDialog}
                  sx={{ borderRadius: 2, mr: 2 }}
                >
                  {t('profile.editTeachingInfo')}
                </Button>
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<LockIcon />}
                  onClick={handleOpenPasswordDialog}
                  sx={{ borderRadius: 2 }}
                >
                  {t('profile.changePassword')}
                </Button>
              </Box>
            </Box>

            {/* مربع حالة طلب التعديل */}
            {profileUpdateStatus && (
              <Paper
                elevation={2}
                sx={{
                  p: 2,
                  mb: 3,
                  borderRadius: 2,
                  backgroundColor: profileUpdateStatus.status === 'pending' ? '#fff3cd' :
                                 profileUpdateStatus.status === 'approved' ? '#d1edff' : '#f8d7da'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  {profileUpdateStatus.status === 'pending' && <WarningIcon color="warning" sx={{ mr: 1 }} />}
                  {profileUpdateStatus.status === 'approved' && <CheckCircleIcon color="success" sx={{ mr: 1 }} />}
                  {profileUpdateStatus.status === 'rejected' && <WarningIcon color="error" sx={{ mr: 1 }} />}
                  <Typography variant="h6" component="h3">
                    {profileUpdateStatus.status === 'pending' && t('profile.updateStatus.pending')}
                    {profileUpdateStatus.status === 'approved' && t('profile.updateStatus.approved')}
                    {profileUpdateStatus.status === 'rejected' && t('profile.updateStatus.rejected')}
                  </Typography>
                </Box>
                <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                  {t('profile.updateStatus.requestDate')}: {new Date(profileUpdateStatus.createdAt).toLocaleDateString('ar-EG')}
                </Typography>
                {profileUpdateStatus.reviewedAt && (
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                    {t('profile.updateStatus.reviewDate')}: {new Date(profileUpdateStatus.reviewedAt).toLocaleDateString('ar-EG')}
                  </Typography>
                )}
                {profileUpdateStatus.adminNotes && (
                  <Box sx={{ mt: 1, p: 1, backgroundColor: 'rgba(0,0,0,0.05)', borderRadius: 1 }}>
                    <Typography variant="body2">
                      <strong>{t('profile.updateStatus.adminNotes')}:</strong> {profileUpdateStatus.adminNotes}
                    </Typography>
                  </Box>
                )}
                {profileUpdateStatus.status === 'pending' && (
                  <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
                    {t('profile.updateStatus.pendingNote')}
                  </Typography>
                )}
              </Paper>
            )}

            <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
              <strong>{t('profile.fullName')}:</strong> {userData.fullName}
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
              <strong>{t('profile.email')}:</strong> {userData.email}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              <strong>{t('profile.gender')}:</strong> {t(`gender.${userData.gender}`)}
            </Typography>
          </Paper>

          {/* Sección de compromiso del profesor */}
          <Paper elevation={3} sx={{ p: 4, mb: 4, borderRadius: 2, bgcolor: 'background.paper' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box>
                <Typography variant="h5" gutterBottom sx={{ mb: 0 }}>
                  {t('teacher.commitment') || 'تعهد المعلم'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {t('teacher.commitmentProfileDescription') || 'حالة التعهد الخاص بك كمعلم في المنصة'}
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  p: 1,
                  borderRadius: 1,
                  bgcolor: profileData.commitmentAccepted ? 'success.light' : 'error.light',
                  color: 'white'
                }}
              >
                {profileData.commitmentAccepted ? (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center' }}>
                    <CheckCircleIcon sx={{ mr: 1 }} />
                    {t('teacher.commitmentStatus.accepted') || 'تم الموافقة على التعهد'}
                  </Typography>
                ) : (
                  <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center' }}>
                    <WarningIcon sx={{ mr: 1 }} />
                    {t('teacher.commitmentStatus.rejected') || 'لم توافق على التعهد بعد'}
                  </Typography>
                )}
              </Box>
            </Box>
            {/* Eliminado el botón de leer y aceptar el compromiso */}
          </Paper>

          {/* Botón para editar la aplicación del profesor */}
          {applicationStatus === 'approved' && (
            <Paper elevation={3} sx={{ p: 4, mb: 4, borderRadius: 2, bgcolor: 'background.paper' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box>
                  <Typography variant="h5" gutterBottom sx={{ mb: 0 }}>
                    {t('teacher.application.title')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('teacher.application.editDescription')}
                  </Typography>
                </Box>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<EditIcon />}
                  onClick={handleEditApplication}
                  sx={{ borderRadius: 2 }}
                >
                  {t('teacher.application.edit')}
                </Button>
              </Box>
            </Paper>
          )}

          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('teacher.profile.personalInfo')}
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('teacher.profile.country')}
                  value={profileData.country}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('teacher.profile.residence')}
                  value={profileData.residence}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('teacher.profile.nativeLanguage')}
                  value={profileData.nativeLanguage}
                  disabled
                />
              </Grid>
            </Grid>

            <Typography variant="h6" sx={{ mt: 4, mb: 2 }}>
              {t('teacher.profile.teachingInfo')}
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>{t('teacher.profile.teachingLanguages.title')}</InputLabel>
                  <Select
                    multiple
                    value={profileData.teachingLanguages}
                    disabled
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected?.map((value) => (
                          <Chip key={value} label={value} />
                        )) || []}
                      </Box>
                    )}
                  >
                    {profileData.teachingLanguages?.map((lang) => (
                      <MenuItem key={lang} value={lang}>
                        {lang}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>{t('teacher.profile.courseTypes')}</InputLabel>
                  <Select
                    multiple
                    value={profileData.courseTypes}
                    disabled
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected?.map((value) => (
                          <Chip key={value} label={value} />
                        )) || []}
                      </Box>
                    )}
                  >
                    {profileData.courseTypes?.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('teacher.profile.pricePerLesson')}
                  value={profileData.pricePerLesson}
                  disabled
                  InputProps={{
                    startAdornment: <InputAdornment position="start">$</InputAdornment>,
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('teacher.profile.trialLessonPrice')}
                  value={profileData.trialLessonPrice}
                  disabled
                  InputProps={{
                    startAdornment: <InputAdornment position="start">$</InputAdornment>,
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('teacher.profile.timezone')}
                  value={profileData.timezone}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('teacher.profile.qualifications')}
                  value={profileData.qualifications}
                  disabled
                  multiline
                  rows={3}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('teacher.profile.teachingExperience')}
                  value={profileData.teachingExperience}
                  disabled
                />
              </Grid>
              <Grid item xs={12}>
                <Paper elevation={2} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>
                        {t('teacher.availableHours')}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {t('teacher.availableHoursProfileDescription')}
                      </Typography>
                    </Box>
                    <Box>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() => navigate('/teacher/view-hours')}
                        sx={{ borderRadius: 2 }}
                      >
                        {t('teacher.viewAvailableHours')}
                      </Button>
                    </Box>
                  </Box>
                </Paper>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  {t('teacher.introVideo')}
                </Typography>
                {profileData.introVideoUrl ? (
                  isYoutubeVideo(profileData.introVideoUrl) ? (
                    // YouTube video
                    <Box sx={{ position: 'relative', paddingTop: '56.25%' /* 16:9 Aspect Ratio */ }}>
                      <ReactPlayer
                        url={profileData.introVideoUrl}
                        width="100%"
                        height="100%"
                        controls
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                        }}
                        config={{
                          youtube: {
                            playerVars: { origin: window.location.origin }
                          }
                        }}
                      />
                    </Box>
                  ) : isLocalVideoFile(profileData.introVideoUrl) ? (
                    // Local video file
                    <Box sx={{ position: 'relative', paddingTop: '56.25%' /* 16:9 Aspect Ratio */ }}>
                      <video
                        src={`https://allemnionline.com${profileData.introVideoUrl}`}
                        width="100%"
                        height="100%"
                        controls
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                        }}
                      />
                    </Box>
                  ) : (
                    // External video URL (not YouTube)
                    <Box sx={{ textAlign: 'center', p: 2 }}>
                      <Typography variant="body1">
                        <a href={profileData.introVideoUrl} target="_blank" rel="noopener noreferrer">
                          {t('teacher.openVideo')}
                        </a>
                      </Typography>
                    </Box>
                  )
                ) : (
                  <Paper
                    elevation={0}
                    variant="outlined"
                    sx={{
                      p: 2,
                      backgroundColor: 'background.default',
                      textAlign: 'center',
                      color: 'text.secondary'
                    }}
                  >
                    {t('teacher.noIntroVideo')}
                  </Paper>
                )}
              </Grid>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  {t('teacher.cv')}
                </Typography>
                <Paper
                  elevation={0}
                  variant="outlined"
                  sx={{
                    p: 2,
                    backgroundColor: 'background.default',
                    whiteSpace: 'pre-wrap'  // This preserves line breaks
                  }}
                >
                  {profileData.cv}
                </Paper>
              </Grid>
            </Grid>
          </Paper>
        </Paper>
      </Container>

      {/* Password Update Dialog */}
      <Dialog
        open={openPasswordDialog}
        onClose={handleClosePasswordDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {t('profile.changePassword')}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, minWidth: 300 }}>
            {error && (
              <Alert severity="error">
                {error}
              </Alert>
            )}
            {success && (
              <Alert severity="success">
                {success}
              </Alert>
            )}
            <TextField
              label={t('profile.currentPassword')}
              type={showPassword.currentPassword ? 'text' : 'password'}
              value={passwordData.currentPassword}
              onChange={handlePasswordChange('currentPassword')}
              required
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label={t('profile.togglePasswordVisibility')}
                      onClick={() => handleClickShowPassword('currentPassword')}
                      edge="end"
                      type="button"
                    >
                      {showPassword.currentPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              label={t('profile.newPassword')}
              type={showPassword.newPassword ? 'text' : 'password'}
              value={passwordData.newPassword}
              onChange={handlePasswordChange('newPassword')}
              required
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label={t('profile.togglePasswordVisibility')}
                      onClick={() => handleClickShowPassword('newPassword')}
                      edge="end"
                      type="button"
                    >
                      {showPassword.newPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              label={t('profile.confirmPassword')}
              type={showPassword.confirmPassword ? 'text' : 'password'}
              value={passwordData.confirmPassword}
              onChange={handlePasswordChange('confirmPassword')}
              required
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label={t('profile.togglePasswordVisibility')}
                      onClick={() => handleClickShowPassword('confirmPassword')}
                      edge="end"
                      type="button"
                    >
                      {showPassword.confirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePasswordDialog}>{t('common.cancel')}</Button>
          <Button type="submit" variant="contained" disabled={loading} onClick={handleUpdatePassword}>
            {loading ? <CircularProgress size={24} /> : t('common.save')}
          </Button>
        </DialogActions>
      </Dialog>



      {/* Edit Teaching Info Dialog */}
      <Dialog
        open={openEditTeachingInfoDialog}
        onClose={handleCloseEditTeachingInfoDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {t('profile.editTeachingInfo')}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, minWidth: 300, mt: 2 }}>
            {error && (
              <Alert severity="error">
                {error}
              </Alert>
            )}
            {success && (
              <Alert severity="success">
                {success}
              </Alert>
            )}

            <Grid container spacing={2}>
              {/* Phone */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('teacher.phone')}
                  value={editTeachingData.phone}
                  onChange={handleEditTeachingChange('phone')}
                  error={!!fieldErrors.phone}
                  helperText={fieldErrors.phone || t('teacher.phoneHelp')}
                />
              </Grid>

              {/* Native Language */}
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  label={t('teacher.nativeLanguage')}
                  value={editTeachingData.nativeLanguage}
                  onChange={handleEditTeachingChange('nativeLanguage')}
                  error={!!fieldErrors.nativeLanguage}
                  helperText={fieldErrors.nativeLanguage || ''}
                />
              </Grid>

              {/* Teaching Languages */}
              <Grid item xs={12}>
                <FormControl fullWidth required error={!!fieldErrors.teachingLanguages}>
                  <InputLabel>{t('teacher.teachingLanguages')}</InputLabel>
                  <Select
                    multiple
                    value={editTeachingData.teachingLanguages}
                    onChange={handleEditTeachingChange('teachingLanguages')}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected?.map((value) => (
                          <Chip
                            key={value}
                            label={languages.find(lang => lang.id === value)?.name || value}
                          />
                        )) || []}
                      </Box>
                    )}
                  >
                    {languages.map((lang) => (
                      <MenuItem key={lang.id} value={lang.id}>
                        {lang.name}
                      </MenuItem>
                    ))}
                  </Select>
                  {fieldErrors.teachingLanguages && (
                    <Typography variant="body2" color="error">
                      {fieldErrors.teachingLanguages}
                    </Typography>
                  )}
                </FormControl>
              </Grid>

              {/* Course Types */}
              <Grid item xs={12}>
                <FormControl fullWidth required error={!!fieldErrors.courseTypes}>
                  <InputLabel>{t('teacher.courseTypes')}</InputLabel>
                  <Select
                    multiple
                    value={editTeachingData.courseTypes}
                    onChange={handleEditTeachingChange('courseTypes')}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected?.map((value) => (
                          <Chip
                            key={value}
                            label={categories.find(cat => cat.id === value)?.name || value}
                          />
                        )) || []}
                      </Box>
                    )}
                  >
                    {categories.map((category) => (
                      <MenuItem key={category.id} value={category.id}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                  {fieldErrors.courseTypes && (
                    <Typography variant="body2" color="error">
                      {fieldErrors.courseTypes}
                    </Typography>
                  )}
                </FormControl>
              </Grid>

              {/* Price per Lesson */}
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  type="number"
                  label={t('teacher.pricePerLesson')}
                  placeholder={t('teacher.pricePerLessonPlaceholder')}
                  value={editTeachingData.pricePerLesson}
                  onChange={handleEditTeachingChange('pricePerLesson')}
                  error={!!fieldErrors.pricePerLesson}
                  helperText={fieldErrors.pricePerLesson || ''}
                  InputProps={{
                    startAdornment: <InputAdornment position="start">$</InputAdornment>,
                  }}
                  inputProps={{ min: 3, max: 100 }}
                />

                {/* Teacher's Earnings Display */}
                {editTeachingData.pricePerLesson > 0 && (
                  <Box sx={{ mt: 1, p: 1.5, bgcolor: 'background.paper', border: '1px dashed', borderColor: 'primary.main', borderRadius: 1 }}>
                    <Typography variant="subtitle2" color="primary" gutterBottom>
                      {t('teacher.yourEarnings') || 'ما ستحصل عليه بعد خصم العمولة:'}
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                      ${calculateTeacherEarnings(editTeachingData.pricePerLesson).toFixed(2)}
                    </Typography>
                  </Box>
                )}
              </Grid>

              {/* Trial Lesson Price */}
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  type="number"
                  label={t('teacher.trialLessonPrice')}
                  placeholder={t('teacher.trialLessonPricePlaceholder')}
                  value={editTeachingData.trialLessonPrice}
                  onChange={handleEditTeachingChange('trialLessonPrice')}
                  error={!!fieldErrors.trialLessonPrice}
                  helperText={fieldErrors.trialLessonPrice || ''}
                  InputProps={{
                    startAdornment: <InputAdornment position="start">$</InputAdornment>,
                  }}
                  inputProps={{ min: 1, max: 99 }}
                />
              </Grid>

              {/* Timezone */}
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required error={!!fieldErrors.timezone}>
                  <InputLabel>{t('teacher.timezone')}</InputLabel>
                  <Select
                    value={editTeachingData.timezone}
                    onChange={handleEditTeachingChange('timezone')}
                  >
                    {timezones.map((timezone) => (
                      <MenuItem key={timezone.value} value={timezone.value}>
                        {timezone.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {fieldErrors.timezone && (
                    <Typography variant="body2" color="error">
                      {fieldErrors.timezone}
                    </Typography>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEditTeachingInfoDialog}>{t('common.cancel')}</Button>
          <Button type="submit" variant="contained" disabled={loading} onClick={handleEditTeachingSubmit}>
            {loading ? <CircularProgress size={24} /> : t('common.save')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Application Dialog */}
      <Dialog
        open={openEditApplicationDialog}
        onClose={() => setOpenEditApplicationDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ bgcolor: 'warning.light', color: 'warning.contrastText' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WarningIcon />
            {t('teacher.application.warningTitle')}
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              {t('teacher.application.warningMessage')}
            </Alert>
            <Typography variant="body1" paragraph>
              {t('teacher.application.warningDescription1')}
            </Typography>
            <Typography variant="body1" paragraph>
              {t('teacher.application.warningDescription2')}
            </Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                ملاحظة: ستستمر في التدريس بالبيانات الحالية حتى يتم مراجعة طلب التعديل والموافقة عليه من قبل الإدارة.
              </Typography>
            </Alert>
            <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
              {t('teacher.application.warningConfirmation')}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenEditApplicationDialog(false)}>
            {t('common.cancel')}
          </Button>
          <Button
            variant="contained"
            color="warning"
            onClick={prepareApplicationEdit}
          >
            {t('teacher.application.confirmEdit')}
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default Profile;
