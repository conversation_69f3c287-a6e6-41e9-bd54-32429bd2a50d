import React, { useEffect, useState } from 'react';
import { 
  Container, 
  Typography, 
  Grid, 
  Paper, 
  Box, 
  Alert,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Rating,
  Divider,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  School as SchoolIcon,
  Group as GroupIcon,
  Star as StarIcon,
  MonetizationOn as MoneyIcon,
  Event as EventIcon,
  Category as CategoryIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';

const StatCard = ({ icon, title, value, color }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        {icon}
        <Typography variant="h6" sx={{ ml: 1 }}>
          {title}
        </Typography>
      </Box>
      <Typography variant="h4" component="div" color={color}>
        {value}
      </Typography>
    </CardContent>
  </Card>
);

const TeacherDashboard = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [dashboardData, setDashboardData] = useState({
    stats: {
      totalStudents: 0,
      totalClasses: 0,
      averageRating: 'N/A',
      totalEarnings: 0
    },
    recentBookings: [],
    recentReviews: [],
    categories: []
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const response = await axios.get('/teacher/dashboard/stats');
        if (response.data.success) {
          setDashboardData(response.data.data);
        } else {
          setError(t('dashboard.fetchError'));
        }
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError(t('dashboard.fetchError'));
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [t]);

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          {t('dashboard.welcome')}, {user?.full_name}
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}
        
        <Grid container spacing={3}>
          {/* Statistics Cards */}
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              icon={<GroupIcon color="primary" />}
              title={t('dashboard.totalStudents')}
              value={dashboardData.stats.totalStudents}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              icon={<SchoolIcon color="secondary" />}
              title={t('dashboard.totalClasses')}
              value={dashboardData.stats.totalClasses}
              color="secondary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              icon={<StarIcon sx={{ color: '#FFD700' }} />}
              title={t('dashboard.averageRating')}
              value={Number(dashboardData.stats.averageRating).toFixed(1)}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              icon={<MoneyIcon color="success" />}
              title={t('dashboard.totalEarnings')}
              value={`$${Number(dashboardData.stats.totalEarnings).toFixed(2)}`}
              color="success"
            />
          </Grid>

          {/* Categories */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CategoryIcon color="primary" />
                <Typography variant="h6" sx={{ ml: 1 }}>
                  {t('dashboard.categories')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {dashboardData.categories.map((category) => (
                  <Chip
                    key={category.id}
                    label={category.name}
                    color="primary"
                    variant="outlined"
                  />
                ))}
              </Box>
            </Paper>
          </Grid>

          {/* Recent Bookings */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <EventIcon color="primary" />
                <Typography variant="h6" sx={{ ml: 1 }}>
                  {t('dashboard.recentBookings')}
                </Typography>
              </Box>
              <List>
                {dashboardData.recentBookings.map((booking) => (
                  <React.Fragment key={booking.id}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar>
                          <EventIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={booking.student_name}
                        secondary={new Date(booking.datetime).toLocaleString()}
                      />
                      <Chip
                        label={booking.status}
                        color={booking.status === 'completed' ? 'success' : 'primary'}
                        size="small"
                      />
                    </ListItem>
                    <Divider variant="inset" component="li" />
                  </React.Fragment>
                ))}
              </List>
            </Paper>
          </Grid>

          {/* Recent Reviews */}
          <Grid item xs={12}>
            <Paper sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <StarIcon color="primary" />
                <Typography variant="h6" sx={{ ml: 1 }}>
                  {t('dashboard.recentReviews')}
                </Typography>
              </Box>
              <List>
                {dashboardData.recentReviews.map((review) => (
                  <React.Fragment key={review.id}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar>
                          <StarIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={review.student_name}
                        secondary={review.comment}
                      />
                      <Rating value={review.rating} readOnly />
                    </ListItem>
                    <Divider variant="inset" component="li" />
                  </React.Fragment>
                ))}
              </List>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default TeacherDashboard;
