import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  Box,
  Fade,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  useTheme,
  alpha,
  Chip,
  Alert,
} from '@mui/material';
import {
  Email as EmailIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  AccessTime as AccessTimeIcon,
  Payment as PaymentIcon,
} from '@mui/icons-material';

const RefundPolicy = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';
  const refund = t('policies.refund', { returnObjects: true });

  const SectionCard = ({ children, bg }) => (
    <Card
      elevation={2}
      sx={{
        mb: 4,
        borderRadius: 3,
        ...(isRtl
          ? { borderRight: `6px solid ${theme.palette.primary.main}` }
          : { borderLeft: `6px solid ${theme.palette.primary.main}` }),
        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),
      }}
    >
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,
        pt: 4,
        pb: 8,
      }}
    >
      <Container maxWidth="lg">
        <Fade in timeout={800}>
          <Box sx={{ p: { xs: 2, md: 4 }, direction: isRtl ? 'rtl' : 'ltr' }}>
            <Typography variant="h4" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
              {refund.title}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary" mb={3}>
              {refund.subtitle}
            </Typography>
            <Divider sx={{ mb: 3 }} />
            {/* Section 1 */}
            <SectionCard>
              <Typography variant="h5" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>{refund.section1.title}</Typography>
              <Typography mb={1}>{refund.section1.description}</Typography>
              <Alert icon={<EmailIcon />} severity="info" sx={{ mb: 1, direction: isRtl ? 'rtl' : 'ltr' }}>{refund.section1.email}</Alert>
              <Typography mb={1}>{refund.section1.requirements}</Typography>
              <List>
                {refund.section1.requirementsList.map((item, idx) => (
                  <ListItem key={idx} sx={{
                      pl: isRtl ? 0 : 2,
                      pr: isRtl ? 2 : 0,
                      flexDirection: 'row',
                      textAlign: isRtl ? 'right' : 'left',
                    }}>
                    <ListItemIcon sx={{
                    minWidth: 'auto',
                    mx: isRtl ? 0 : 1,
                    ml: isRtl ? 1 : 0,
                  }}>{isRtl ? <CheckCircleIcon /> : <CheckCircleIcon />}</ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                ))}
              </List>
              <Alert icon={<InfoIcon />} severity="warning" sx={{ mt: 2, direction: isRtl ? 'rtl' : 'ltr' }}>{refund.section1.note}</Alert>
            </SectionCard>
            {/* Section 2 */}
            <SectionCard>
              <Typography variant="h5" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>{refund.section2.title}</Typography>
              <List>
                {refund.section2.cases.map((item, idx) => (
                  <ListItem key={idx} alignItems="flex-start" sx={{
                      pl: isRtl ? 0 : 2,
                      pr: isRtl ? 2 : 0,
                      flexDirection: 'row',
                      textAlign: isRtl ? 'right' : 'left',
                    }}>
                    <ListItemIcon sx={{
                    minWidth: 'auto',
                    mx: isRtl ? 0 : 1,
                    ml: isRtl ? 1 : 0,
                  }}><CheckCircleIcon color="success" /></ListItemIcon>
                    <ListItemText
                      primary={<><b>{item.condition}</b></>}
                      secondary={item.result}
                    />
                  </ListItem>
                ))}
              </List>
            </SectionCard>
            {/* Section 3 */}
            <SectionCard>
              <Typography variant="h5" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>{refund.section3.title}</Typography>
              <List>
                {refund.section3.cases.map((item, idx) => (
                  <ListItem key={idx} sx={{
                      pl: isRtl ? 0 : 2,
                      pr: isRtl ? 2 : 0,
                      flexDirection: 'row',
                      textAlign: isRtl ? 'right' : 'left',
                    }}>
                    <ListItemIcon sx={{
                    minWidth: 'auto',
                    mx: isRtl ? 0 : 1,
                    ml: isRtl ? 1 : 0,
                  }}><CancelIcon color="error" /></ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                ))}
              </List>
            </SectionCard>
            {/* Section 4 */}
            <SectionCard>
              <Typography variant="h5" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>{refund.section4.title}</Typography>
              <List>
                {refund.section4.steps.map((item, idx) => (
                  <ListItem key={idx} sx={{
                      pl: isRtl ? 0 : 2,
                      pr: isRtl ? 2 : 0,
                      flexDirection: 'row',
                      textAlign: isRtl ? 'right' : 'left',
                    }}>
                    <ListItemIcon sx={{
                    minWidth: 'auto',
                    mx: isRtl ? 0 : 1,
                    ml: isRtl ? 1 : 0,
                  }}><ScheduleIcon /></ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                ))}
              </List>
            </SectionCard>
            {/* Section 5 */}
            <SectionCard>
              <Typography variant="h5" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>{refund.section5.title}</Typography>
              <List>
                {refund.section5.limits.map((item, idx) => (
                  <ListItem key={idx} sx={{
                      pl: isRtl ? 0 : 2,
                      pr: isRtl ? 2 : 0,
                      flexDirection: 'row',
                      textAlign: isRtl ? 'right' : 'left',
                    }}>
                    <ListItemIcon sx={{
                    minWidth: 'auto',
                    mx: isRtl ? 0 : 1,
                    ml: isRtl ? 1 : 0,
                  }}><WarningIcon color="warning" /></ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                ))}
              </List>
            </SectionCard>
            {/* Section 6 */}
            <SectionCard>
              <Typography variant="h5" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>{refund.section6.title}</Typography>
              <Typography>{refund.section6.description}</Typography>
            </SectionCard>
            {/* Contact */}
            <Divider sx={{ my: 3 }} />
            <Box>
              <Typography variant="h6" fontWeight={600} mb={1}>{refund.contact.title}</Typography>
              <Typography mb={1}>{refund.contact.description}</Typography>
              <Alert icon={<EmailIcon />} severity="info" sx={{ direction: isRtl ? 'rtl' : 'ltr' }}>{refund.contact.email}</Alert>
            </Box>
          </Box>
        </Fade>
      </Container>
    </Box>
  );
};

export default RefundPolicy;